<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - RestroManager</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- PWA Icons - with fallbacks -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 32 32%22><rect width=%2232%22 height=%2232%22 fill=%22%232563eb%22/><text x=%2216%22 y=%2220%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2212%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 16 16%22><rect width=%2216%22 height=%2216%22 fill=%22%232563eb%22/><text x=%228%22 y=%2212%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%228%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 180 180%22><rect width=%22180%22 height=%22180%22 fill=%22%232563eb%22/><text x=%2290%22 y=%22110%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2260%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Tailwind CSS configuration
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>

    <style>
        /* Fallback styles in case Tailwind doesn't load */
        .fallback-styles {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fallback-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 1200px;
            width: 100%;
            overflow: hidden;
        }

        .fallback-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 32px;
            border-bottom: 1px solid #e2e8f0;
        }

        .fallback-content {
            padding: 32px;
        }

        .fallback-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 16px;
        }

        .fallback-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            font-size: 16px;
        }

        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --success-500: #10b981;
            --error-500: #ef4444;
            --warning-500: #f59e0b;
        }

        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        .main-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .top-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
            border-bottom: 1px solid #e2e8f0;
        }

        .step-section {
            display: none;
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step-section.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .step-indicator {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step-indicator .step-circle {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step-indicator.active .step-circle {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
            transform: scale(1.1);
        }
        
        .step-indicator.active .step-label {
             color: #1e293b;
             font-weight: 600;
        }

        .step-indicator.completed .step-circle {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .step-indicator.completed .step-label {
             color: #1e293b;
             font-weight: 600;
        }
        
        .step-connector {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background-color: #e2e8f0;
        }

        .step-indicator.completed .step-connector {
            background: linear-gradient(to right, #10b981, #059669);
        }
        
        .step-indicator.active .step-connector {
             background: linear-gradient(to right, #10b981, #e2e8f0);
        }


        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .form-input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
        }

        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .success-message {
            color: #10b981;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
        }

        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-1px);
        }

        .selectable-option {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .selectable-option input:checked + label {
            border-color: #2563eb;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            color: #1d4ed8;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
            transform: translateY(-2px);
        }

        .selectable-option label:hover {
            border-color: #93c5fd;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-row {
            animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transition: all 0.3s ease;
        }

        .table-row:hover {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transform: translateX(4px);
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .cuisine-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .drag-drop-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .drag-drop-area.dragover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
        }
        
        #floor-plan-sections {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .floor-plan-section {
            border: 2px dashed #cbd5e1;
            border-radius: 0.75rem;
            padding: 1rem;
        }

        .floor-plan-section-title {
            font-weight: 600;
            color: #475569;
            margin-bottom: 0.75rem;
        }

        .floor-plan-table-grid {
             display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 1rem;
        }
        
        .price-slider-thumb::-webkit-slider-thumb {
            -webkit-appearance: none;
            margin-top: -8px; /* Offset to center the thumb on the track */
            height: 1.25rem;
            width: 1.25rem;
            background-color: white;
            border: 2px solid #2563eb;
            border-radius: 9999px;
            cursor: pointer;
            pointer-events: auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .price-slider-thumb::-moz-range-thumb {
            height: 1.25rem;
            width: 1.25rem;
            background-color: white;
            border: 2px solid #2563eb;
            border-radius: 9999px;
            cursor: pointer;
            pointer-events: auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        @media (max-width: 768px) {
            .step-section {
                padding: 1rem;
            }

            .main-container {
                margin: 0.5rem;
                border-radius: 1rem;
            }
        }
    </style>
</head>
<body class="bg-slate-100">
    <!-- Loading indicator -->
    <div id="loading-indicator" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #f1f5f9; display: flex; align-items: center; justify-content: center; z-index: 9999;">
        <div style="text-align: center;">
            <div style="width: 40px; height: 40px; border: 4px solid #e2e8f0; border-top: 4px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
            <p style="color: #64748b; font-family: Inter, sans-serif;">Loading RestroManager...</p>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-5xl flex flex-col main-container rounded-2xl overflow-hidden my-8">
            <!-- Top Panel: Progress & Branding -->
            <div class="w-full top-panel p-8">
                <div class="flex justify-between items-center mb-6">
                    <a href="#" class="flex items-center gap-3 group">
                        <div class="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
                    </a>
                    <div class="text-sm text-slate-500" id="progress-text">Step 1 of 6 - Account Details</div>
                </div>

                <div id="step-indicators-container" class="w-full flex items-center">
                    <!-- Step indicators will be dynamically generated here -->
                </div>
            </div>

            <!-- Bottom Panel: Form -->
            <div class="w-full p-8 sm:p-14">
                <form id="signup-form">
                    <!-- Step 1: Create Account -->
                    <div id="step-1" class="step-section active">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Create your account</h2>
                            <p class="text-slate-500">Start by setting up your login details.</p>
                        </div>

                        <div class="space-y-6">
                            <button type="button" class="w-full flex items-center justify-center gap-3 py-4 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group">
                                <img src="https://www.google.com/favicon.ico" alt="Google icon" class="w-5 h-5 group-hover:scale-110 transition-transform">
                                <span class="text-sm font-medium text-slate-700">Sign up with Google</span>
                            </button>

                            <div class="flex items-center">
                                <div class="flex-grow border-t border-slate-200"></div>
                                <span class="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                                <div class="flex-grow border-t border-slate-200"></div>
                            </div>

                            <div class="space-y-4">
                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="mail" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="email" name="email" required placeholder="Email address"
                                               class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div class="validation-message"></div>
                                </div>

                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="lock" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="password" name="password" required placeholder="Password (min. 8 characters)"
                                               class="form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <button type="button" class="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                                            <i data-lucide="eye" class="w-5 h-5"></i>
                                        </button>
                                    </div>
                                    <div class="validation-message"></div>
                                    <div class="password-strength mt-2 hidden">
                                        <div class="flex gap-1 mb-1">
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                        </div>
                                        <p class="strength-text text-xs text-slate-500"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="button" data-next="1" class="next-btn btn-primary w-full px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Continue</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Business & Cuisine -->
                    <div id="step-2" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Business & Cuisine</h2>
                            <p class="text-slate-500">Tell us about your business and what you serve.</p>
                        </div>

                        <div class="space-y-6">
                            <div class="form-group">
                                <label for="business-name" class="block text-sm font-semibold text-slate-700 mb-2">Business Name</label>
                                <div class="relative">
                                    <i data-lucide="store" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="business-name" name="businessName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Enter your business name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="business-type" class="block text-sm font-semibold text-slate-700 mb-2">Business Type</label>
                                <div class="relative">
                                    <i data-lucide="utensils" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <select id="business-type" name="businessType"
                                            class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select business type</option>
                                        <option value="Fine Dining">🍽️ Fine Dining</option>
                                        <option value="Casual Dining">🍕 Casual Dining</option>
                                        <option value="Cafe / Bistro">☕ Cafe / Bistro</option>
                                        <option value="Quick Service (QSR)">🍔 Quick Service (QSR)</option>
                                        <option value="Food Truck">🚚 Food Truck</option>
                                        <option value="Pub / Bar">🍺 Pub / Bar</option>
                                        <option value="Bakery">🥖 Bakery</option>
                                    </select>
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-4">Cuisine Type (select all that apply)</label>
                                <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-british" name="cuisine" value="British" class="hidden"><label for="cuisine-british" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🇬🇧</div><div class="text-sm">British</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-indian" name="cuisine" value="Indian" class="hidden"><label for="cuisine-indian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍛</div><div class="text-sm">Indian</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-italian" name="cuisine" value="Italian" class="hidden"><label for="cuisine-italian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍝</div><div class="text-sm">Italian</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-chinese" name="cuisine" value="Chinese" class="hidden"><label for="cuisine-chinese" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🥢</div><div class="text-sm">Chinese</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-american" name="cuisine" value="American" class="hidden"><label for="cuisine-american" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍔</div><div class="text-sm">American</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-other" name="cuisine" value="Other" class="hidden"><label for="cuisine-other" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍽️</div><div class="text-sm">Other</div></label></div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="0" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="2" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Operations & Daily Revenue -->
                    <div id="step-3" class="step-section">
                        <div class="mb-8">
                             <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Operations & Daily Revenue</h2>
                            <p class="text-slate-500">Plan your staffing and operations for each service with precision.</p>
                        </div>
                        <div id="operations-schedule-container" class="space-y-6 max-h-[60vh] overflow-y-auto pr-4">
                            <!-- JS generates the weekly schedule here -->
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="1" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="3" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 4: Table Configuration -->
                    <div id="step-4" class="step-section">
                        <div class="mb-6">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">Table Configuration</h2>
                            <p class="text-slate-500">Design your restaurant's floor plan.</p>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Left: Controls -->
                            <div class="space-y-6">
                                <div class="form-group">
                                    <label class="block text-sm font-semibold text-slate-700 mb-2">Layout Style</label>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="selectable-option">
                                            <input type="radio" id="layout-section" name="layoutStyle" value="section" class="hidden" checked>
                                            <label for="layout-section" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="layout-grid" class="mx-auto mb-2"></i>Section-based</label>
                                        </div>
                                        <div class="selectable-option">
                                            <input type="radio" id="layout-flow" name="layoutStyle" value="flow" class="hidden">
                                            <label for="layout-flow" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="move-3d" class="mx-auto mb-2"></i>Free Flow</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Based UI -->
                                <div id="section-based-ui">
                                    <button type="button" id="show-add-section-modal-btn" class="w-full btn-secondary py-3 text-sm font-semibold rounded-lg flex items-center justify-center gap-2">
                                        <i data-lucide="plus" class="w-4 h-4"></i> Add New Section
                                    </button>
                                    <div id="table-sections-container" class="mt-4 space-y-4 max-h-[400px] overflow-y-auto pr-2">
                                        <!-- JS generates sections here -->
                                    </div>
                                </div>

                                <!-- Flow Based UI -->
                                <div id="flow-based-ui" class="hidden">
                                     <div class="flex justify-between items-center">
                                        <h3 class="text-lg font-semibold text-slate-700">Tables</h3>
                                        <button type="button" id="add-flow-table-btn" class="btn-secondary px-4 py-2 text-sm font-semibold text-slate-700 rounded-lg flex items-center gap-2">
                                            <i data-lucide="plus" class="w-4 h-4"></i> Add Table
                                        </button>
                                    </div>
                                    <div id="flow-table-list" class="mt-4 space-y-3 max-h-[400px] overflow-y-auto pr-2">
                                        <!-- JS generates flow tables here -->
                                    </div>
                                </div>
                            </div>
                            <!-- Right: Preview -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-slate-700">Floor Plan Preview</h3>
                                <div id="floor-plan-preview" class="bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-4 min-h-[400px]">
                                    <!-- JS generates preview here -->
                                </div>
                            </div>
                        </div>


                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="2" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i> Back
                            </button>
                            <button type="button" data-next="4" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span> Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 5: Summary -->
                    <div id="step-5" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Review & Confirm</h2>
                            <p class="text-slate-500">Please review all your information before proceeding.</p>
                        </div>

                        <div id="summary-container" class="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm">
                            <!-- Summary content will be injected here -->
                        </div>

                        <div class="mt-8">
                            <div class="flex items-center mb-6">
                                <input type="checkbox" id="terms-checkbox" class="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500">
                                <label for="terms-checkbox" class="ml-3 text-sm text-slate-600">
                                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="flex items-center gap-4">
                                <button type="button" data-prev="3" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                    Back
                                </button>
                                <button type="button" data-next="5" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span class="btn-text">Confirm & Continue</span>
                                    <span class="btn-loading hidden">
                                        <span class="loading-spinner"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Personal Details -->
                    <div id="step-6" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Your Personal Details</h2>
                            <p class="text-slate-500">We need this to verify your account.</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="first-name" class="block text-sm font-semibold text-slate-700 mb-2">First Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="first-name" name="firstName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your first name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="last-name" class="block text-sm font-semibold text-slate-700 mb-2">Last Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="last-name" name="lastName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your last name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="phone" class="block text-sm font-semibold text-slate-700 mb-2">Phone Number</label>
                                <div class="relative">
                                    <i data-lucide="phone" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="tel" id="phone" name="phone" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g. 07123456789">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="address" class="block text-sm font-semibold text-slate-700 mb-2">Address</label>
                                <div class="relative">
                                    <i data-lucide="map-pin" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="address" name="address" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Street address">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="city" class="block text-sm font-semibold text-slate-700 mb-2">City</label>
                                <div class="relative">
                                    <i data-lucide="building" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="city" name="city" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your city">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="postcode" class="block text-sm font-semibold text-slate-700 mb-2">Postcode</label>
                                <div class="relative">
                                    <i data-lucide="mail-open" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="postcode" name="postcode" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g. SW1A 0AA">
                                </div>
                                <div class="validation-message"></div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="4" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="submit" class="btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Create Account</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Section Modal -->
    <div id="add-section-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-sm w-full mx-4">
            <h3 class="text-xl font-bold text-slate-800 mb-4">Add New Section</h3>
            <div class="form-group">
                <label for="modal-section-name" class="block text-sm font-semibold text-slate-700 mb-2">Section Name</label>
                <input type="text" id="modal-section-name" placeholder="e.g. Patio, Bar Area" class="form-input w-full">
            </div>
            <div class="mt-6 flex justify-end gap-4">
                <button type="button" id="cancel-add-section" class="btn-secondary px-4 py-2 text-sm font-semibold rounded-lg">Cancel</button>
                <button type="button" id="confirm-add-section" class="btn-primary px-4 py-2 text-sm font-semibold rounded-lg">Add Section</button>
            </div>
        </div>
    </div>


    <script>
        // Check if Tailwind CSS loaded properly
        function checkTailwindLoaded() {
            const testElement = document.createElement('div');
            testElement.className = 'bg-blue-500';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const isLoaded = computedStyle.backgroundColor === 'rgb(59, 130, 246)';

            document.body.removeChild(testElement);

            if (!isLoaded) {
                console.warn('⚠️ Tailwind CSS not loaded properly, applying fallback styles');
                document.body.className = 'fallback-styles';
                const container = document.querySelector('.min-h-screen');
                if (container) {
                    container.className = 'fallback-container';
                }
            }

            return isLoaded;
        }

        // Check for missing icons and provide fallbacks
        function handleMissingIcons() {
            const icons = document.querySelectorAll('[data-lucide]');
            icons.forEach(icon => {
                const iconName = icon.getAttribute('data-lucide');
                // Add error handling for missing icons
                icon.onerror = function() {
                    console.warn(`Icon ${iconName} not found, using fallback`);
                    this.innerHTML = '●'; // Simple fallback
                };
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Check if Tailwind loaded
            setTimeout(checkTailwindLoaded, 100);

            // Handle missing icons
            handleMissingIcons();

            // Initialize Lucide icons with error handling
            try {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                } else {
                    console.warn('⚠️ Lucide icons not loaded, using fallbacks');
                }
            } catch (error) {
                console.warn('⚠️ Error initializing icons:', error);
            }

            // Hide loading indicator
            setTimeout(() => {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.opacity = '0';
                    loadingIndicator.style.transition = 'opacity 0.3s ease';
                    setTimeout(() => {
                        loadingIndicator.style.display = 'none';
                    }, 300);
                }
            }, 500);

            const steps = document.querySelectorAll('.step-section');
            const stepIndicatorsContainer = document.getElementById('step-indicators-container');
            const nextButtons = document.querySelectorAll('.next-btn');
            const prevButtons = document.querySelectorAll('.prev-btn');
            const form = document.getElementById('signup-form');
            const progressText = document.getElementById('progress-text');
            let currentStep = 0;

            const stepConfig = [
                { id: 'account', label: 'Account'},
                { id: 'business', label: 'Business & Cuisine'},
                { id: 'operations', label: 'Operations'},
                { id: 'tables', label: 'Tables'},
                { id: 'summary', label: 'Review'},
                { id: 'personal', label: 'Personal'}
            ];

            // Validation rules (Updated for UK)
            const validationRules = {
                email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Please enter a valid email address' },
                password: { required: true, minLength: 8, message: 'Password must be at least 8 characters long' },
                businessName: { required: true, minLength: 2, message: 'Business name must be at least 2 characters' },
                businessType: { required: true, message: 'Please select a business type' },
                firstName: { required: true, minLength: 2, message: 'First name must be at least 2 characters' },
                lastName: { required: true, minLength: 2, message: 'Last name must be at least 2 characters' },
                phone: { required: true, pattern: /^(?:(?:\(?(?:0(?:0|11)\)?[\s-]?\(?|0)44\)?[\s-]?(?:\(?0\)?[\s-]?)?)|(?:\(?0))(?:(?:\d{5}\)?[\s-]?\d{4,5})|(?:\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3}))|(?:\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4})|(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}))(?:[\s-]?\d{4})?$/, message: 'Please enter a valid UK phone number' },
                address: { required: true, minLength: 5, message: 'Please enter a valid address' },
                city: { required: true, minLength: 2, message: 'Please enter a valid city' },
                postcode: { required: true, pattern: /^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})$/, message: 'Please enter a valid UK postcode' }
            };

            // Form validation
            const validateField = (field) => {
                const fieldName = field.name;
                const fieldValue = field.value.trim();
                const rules = validationRules[fieldName];
                if (!rules) return true;
                const fieldGroup = field.closest('.form-group');
                const validationMessage = fieldGroup ? fieldGroup.querySelector('.validation-message') : null;
                field.classList.remove('error', 'success');
                if (validationMessage) validationMessage.innerHTML = '';
                if (rules.required && fieldValue === '') {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> This field is required</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.minLength && fieldValue.length < rules.minLength) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.pattern && !rules.pattern.test(fieldValue)) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                field.classList.add('success');
                if (validationMessage) validationMessage.innerHTML = `<div class="success-message"><i data-lucide="check-circle" class="w-3 h-3"></i> Looks good!</div>`;
                lucide.createIcons();
                return true;
            };

            const validateStep = (stepIndex) => {
                const currentStepElement = steps[stepIndex];
                const fields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                fields.forEach(field => {
                    if (!field.disabled && !validateField(field)) isValid = false;
                });
                if (stepIndex === 3) { // Table setup is now step 4 (index 3)
                    const layoutStyle = form.querySelector('input[name="layoutStyle"]:checked').value;
                    const tableCount = (layoutStyle === 'section')
                        ? document.querySelectorAll('#table-sections-container .table-row').length
                        : document.querySelectorAll('#flow-table-list .table-row').length;

                    if (tableCount === 0) {
                        alert('Please add at least one table before continuing.');
                        return false;
                    }
                }
                return isValid;
            };

            // Update progress indicators and progress bar
            const updateProgress = () => {
                progressText.textContent = `Step ${currentStep + 1} of ${stepConfig.length} - ${stepConfig[currentStep].label}`;
                stepIndicatorsContainer.innerHTML = '';
                stepConfig.forEach((step, index) => {
                    const isCompleted = index < currentStep;
                    const isActive = index === currentStep;

                    const indicator = document.createElement('div');
                    indicator.className = 'step-indicator flex-1 flex flex-col items-center relative';
                    if(isCompleted) indicator.classList.add('completed');
                    if(isActive) indicator.classList.add('active');

                    indicator.innerHTML = `
                        <div class="step-circle w-10 h-10 rounded-full border-2 border-slate-300 bg-slate-100 flex items-center justify-center text-sm font-semibold text-slate-500 mb-2">
                            ${isCompleted ? '<i data-lucide="check" class="w-4 h-4"></i>' : index + 1}
                        </div>
                        <span class="step-label text-xs text-slate-500 text-center font-medium">${step.label}</span>
                        ${index < stepConfig.length - 1 ? '<div class="step-connector absolute top-5 left-full w-full h-0.5 bg-slate-200"></div>' : ''}
                    `;

                    stepIndicatorsContainer.appendChild(indicator);
                });

                lucide.createIcons();
            };

            const showStep = (stepIndex, skipValidation = false) => {
                if (!skipValidation && stepIndex > currentStep && !validateStep(currentStep)) return;
                performStepChange(stepIndex);
            };

            const performStepChange = (stepIndex) => {
                if (stepIndex === 4) generateSummary(); // Summary is now step 5 (index 4)
                steps.forEach((step, index) => step.classList.toggle('active', index === stepIndex));
                currentStep = stepIndex;
                updateProgress();
            };

            nextButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.next))));
            prevButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.prev), true)));
            form.addEventListener('input', (e) => { if (e.target.matches('input, select, textarea')) validateField(e.target); });

            // Initialize progress
            updateProgress();

            // Placeholder functions for missing functionality
            function generateSummary() {
                console.log('Summary generation placeholder');
            }
        });
    </script>
</body>
</html>
