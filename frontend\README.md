# RestroManager Frontend 🍽️

A modern React + TypeScript + Vite frontend application that replicates the design and functionality of the RestroManager PWA. This project provides a complete restaurant management interface with authentication, dashboard, and PWA capabilities.

## ✨ Features

- **🔐 Authentication System** - Complete sign-in/sign-up flow with role-based access
- **📱 Responsive Design** - Mobile-first design that works on all devices
- **⚡ Fast Performance** - Built with Vite for lightning-fast development and builds
- **🎨 Modern UI** - Beautiful interface using Tailwind CSS and Lucide icons
- **🔄 PWA Ready** - Progressive Web App with offline support and installability
- **🛡️ Type Safety** - Full TypeScript support for better development experience
- **🧭 Client-side Routing** - React Router with protected routes and navigation guards

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open in browser**: `http://localhost:5173`

### Build for Production

```bash
npm run build
npm run preview
```

## 📱 Pages & Features

### 🏠 Home Page (`/`)
- Landing page with automatic authentication detection
- Redirects users based on login status
- Manual navigation options for new users

### 🔑 Sign In Page (`/signin`)
- Role-based authentication (Admin, Manager, Staff, Waiter)
- Form validation with real-time feedback
- Demo account auto-fill for testing
- Remember me functionality
- Responsive design with glass morphism effects

### ✍️ Sign Up Page (`/signup`)
- Multi-step registration process
- Personal info, account setup, and restaurant details
- Comprehensive form validation
- Progress indicator
- Terms and conditions agreement

### 📊 Dashboard Page (`/dashboard`)
- Role-based dashboard interface
- Sidebar navigation with responsive mobile menu
- Statistics cards with real-time data
- Recent orders and quick actions
- User profile and logout functionality

## 🔐 Authentication Flow

### Demo Accounts
The application includes pre-configured demo accounts for testing:

- **Admin**: `<EMAIL>` / `admin123`
- **Manager**: `<EMAIL>` / `manager123`
- **Staff**: `<EMAIL>` / `staff123`
- **Waiter**: `<EMAIL>` / `waiter123`

### Authentication Features
- ✅ Local storage persistence
- ✅ Automatic login state detection
- ✅ Protected routes with navigation guards
- ✅ Role-based access control
- ✅ Secure logout functionality

## 🛠️ Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite 7
- **Styling**: Tailwind CSS with custom components
- **Icons**: Lucide React
- **Routing**: React Router DOM v6
- **State Management**: React Context API
- **PWA**: Service Worker + Web App Manifest
- **Development**: ESLint + TypeScript strict mode

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI components (Button, Input, Modal, etc.)
│   └── ProtectedRoute.tsx
├── contexts/           # React Context providers
│   └── AuthContext.tsx
├── pages/              # Page components
│   ├── Home.tsx
│   ├── SignIn.tsx
│   ├── SignUp.tsx
│   └── Dashboard.tsx
├── types/              # TypeScript type definitions
│   └── index.ts
├── utils/              # Utility functions
│   ├── auth.ts         # Authentication helpers
│   ├── validation.ts   # Form validation
│   └── pwa.ts          # PWA utilities
├── App.tsx             # Main app component with routing
├── main.tsx            # Application entry point
└── index.css           # Global styles and Tailwind imports
```

## 🧪 Testing the Application

### Manual Testing Checklist

1. **Home Page**:
   - [ ] Loads correctly with logo and navigation options
   - [ ] Auto-redirects based on authentication status
   - [ ] Manual navigation buttons work

2. **Sign In Page**:
   - [ ] Role selection works and auto-fills credentials
   - [ ] Form validation shows appropriate errors
   - [ ] Successful login redirects to dashboard
   - [ ] Failed login shows error modal
   - [ ] Password toggle functionality works
   - [ ] Remember me checkbox functions

3. **Sign Up Page**:
   - [ ] Multi-step form navigation works
   - [ ] Form validation on each step
   - [ ] Progress indicator updates correctly
   - [ ] Successful registration creates account and redirects
   - [ ] All form fields validate properly

4. **Dashboard Page**:
   - [ ] Loads with user information
   - [ ] Sidebar navigation works
   - [ ] Mobile menu toggles correctly
   - [ ] Statistics cards display
   - [ ] Logout functionality works
   - [ ] Responsive design on mobile/tablet

5. **PWA Features**:
   - [ ] Service worker registers successfully
   - [ ] Offline page loads when disconnected
   - [ ] App manifest is valid
   - [ ] Install prompt appears (on supported browsers)

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Environment Setup

The application uses environment variables for configuration. Create a `.env` file:

```env
VITE_APP_NAME=RestroManager
VITE_APP_VERSION=1.0.0
```

## 📱 PWA Features

### Service Worker
- Caches static assets for offline use
- Background sync capabilities
- Push notification support

### Web App Manifest
- Installable on mobile and desktop
- Custom app icons and splash screens
- Standalone display mode

### Offline Support
- Cached pages load without internet
- Offline indicator and messaging
- Automatic reconnection detection

## 🎨 Design System

### Colors
- Primary: Blue (#2563eb)
- Success: Green (#10b981)
- Error: Red (#ef4444)
- Warning: Orange (#f59e0b)

### Typography
- Font Family: Inter
- Weights: 400, 500, 600, 700

### Components
- Glass morphism effects
- Smooth animations and transitions
- Consistent spacing and sizing
- Mobile-first responsive design

## 🚀 Deployment

### Build Process
```bash
npm run build
```

### Deployment Options
- **Vercel**: Connect GitHub repo for automatic deployments
- **Netlify**: Drag and drop `dist` folder
- **GitHub Pages**: Use GitHub Actions for CI/CD
- **Traditional Hosting**: Upload `dist` folder contents

### Production Checklist
- [ ] Environment variables configured
- [ ] PWA icons generated and optimized
- [ ] Service worker configured for production domain
- [ ] HTTPS enabled for PWA features
- [ ] Performance optimized (Lighthouse score 90+)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

**RestroManager Frontend** - Built with modern web technologies for the best restaurant management experience! 🚀
