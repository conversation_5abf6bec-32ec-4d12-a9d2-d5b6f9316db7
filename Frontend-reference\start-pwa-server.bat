@echo off
echo.
echo ========================================
echo   RestroManager PWA Development Server
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    echo 💡 Please install Node.js from https://nodejs.org
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js found
echo.

REM Check if we're in the right directory
if not exist "manifest.json" (
    echo ❌ manifest.json not found
    echo 💡 Please run this script from the RestroManager directory
    echo.
    pause
    exit /b 1
)

echo ✅ PWA files found
echo.

REM Check if icons exist
if not exist "icons" (
    echo ⚠️  Icons directory not found!
    echo 💡 Please generate PWA icons:
    echo    1. Open generate-icons.html in your browser
    echo    2. Download all icons
    echo    3. Save them in the icons/ folder
    echo.
)

echo 🚀 Starting PWA server...
echo 📱 Open http://localhost:8000 in your browser
echo.

node server.js 8000

echo.
echo 🛑 Server stopped
pause
