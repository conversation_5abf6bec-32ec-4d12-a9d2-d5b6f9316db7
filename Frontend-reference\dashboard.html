<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroManager - Dashboard</title>

    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <link rel="manifest" href="./manifest.json">

    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <script src="https://cdn.tailwindcss.com"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <script src="https://unpkg.com/lucide@latest"></script>

    <script src="./pwa-utils.js"></script>

    <style>
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --success-50: #f0fdf4;
            --success-500: #22c55e;
            --warning-50: #fffbeb;
            --warning-500: #f59e0b;
            --error-50: #fef2f2;
            --error-500: #ef4444;
        }

        /* Custom base styles */
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #f1f5f9; }
        ::-webkit-scrollbar-thumb { background: #94a3b8; border-radius: 10px; }
        ::-webkit-scrollbar-thumb:hover { background: #64748b; }

        /* Glass morphism effects */
        .glass-card {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stat-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced sidebar styles */
        .sidebar-item {
            transition: all 0.2s ease;
            position: relative;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            transform: translateX(4px);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .sidebar-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #ffffff;
            border-radius: 0 4px 4px 0;
        }

        /* Sidebar transition and base styles */
        #sidebar {
            transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* Collapsed state for the sidebar */
        #sidebar.collapsed {
            width: 5rem; /* w-20 */
        }

        #sidebar.collapsed .sidebar-link {
            justify-content: center;
        }

        #sidebar.collapsed .sidebar-link > i {
            margin-right: 0;
        }

        #sidebar.collapsed .sidebar-link-text,
        #sidebar.collapsed .logo-text,
        #sidebar.collapsed .sidebar-badge,
        #sidebar.collapsed .pro-plan-banner {
            display: none;
        }

        #sidebar.collapsed #sidebar-toggle .lucide-chevrons-left {
            transform: rotate(180deg);
        }
        
        /* Tooltip for collapsed sidebar */
        .sidebar-item .tooltip {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s;
            position: absolute;
            left: 5.5rem; /* Position next to the icon */
            background-color: #1f2937;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            z-index: 50;
        }

        #sidebar.collapsed .sidebar-item:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }

        /* Active link styles */
        .sidebar-link.active {
            background-color: #eff6ff;
            color: #2563eb;
            font-weight: 600;
        }
        .sidebar-link.active svg { color: #ffffff; }

        /* Content section visibility */
        .content-section {
            display: none;
        }
        .content-section.active {
            display: block;
        }

        /* Active filter button style */
        .filter-btn.active {
            background-color: #2563eb;
            color: white;
        }
        
        /* Drag and Drop styles */
        .task-card.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
        }
        .kanban-column .tasks-container.drag-over {
            border-style: dashed;
            border-color: #2563eb;
            background-color: #eff6ff;
        }

        /* Professional utility styles */
        .metric-trend {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 12px;
        }

        .trend-up {
            background: var(--success-50);
            color: var(--success-500);
        }

        .trend-down {
            background: var(--error-50);
            color: var(--error-500);
        }

        .trend-neutral {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: var(--success-50);
            color: var(--success-500);
        }

        .status-pending {
            background: var(--warning-50);
            color: var(--warning-500);
        }

        .status-completed {
            background: var(--primary-50);
            color: var(--primary-600);
        }

        .quick-action {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .quick-action:hover {
            border-color: var(--primary-500);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
        }

        .table-row {
            transition: all 0.2s ease;
        }

        .table-row:hover {
            background: rgba(59, 130, 246, 0.05);
            transform: scale(1.01);
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in {
            animation: slideIn 0.5s ease-out;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 1024px) {
            #sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
            #sidebar.open {
                transform: translateX(0);
            }
            #main-content-wrapper {
                margin-left: 0;
            }
        }

    </style>
</head>
<body class="bg-slate-100">

    <div class="relative h-screen overflow-hidden lg:flex">
        <div id="sidebar-backdrop" class="fixed inset-0 bg-black bg-opacity-50 z-20 hidden lg:hidden"></div>

        <aside id="sidebar" class="w-64 bg-white shadow-lg flex flex-col fixed inset-y-0 left-0 transform lg:relative z-30">
            <div class="h-20 flex items-center justify-center border-b px-4 overflow-hidden flex-shrink-0">
                <a href="#" class="flex items-center gap-2">
                    <i data-lucide="chef-hat" class="w-8 h-8 text-blue-600 flex-shrink-0"></i>
                    <h1 class="text-2xl font-bold text-blue-600 logo-text whitespace-nowrap">Restro<span class="text-slate-800">Manager</span></h1>
                </a>
            </div>
            
            <nav class="flex-1 mt-6 space-y-1 px-3 overflow-y-auto overflow-x-hidden">
                <a href="#" data-target="dashboard" class="sidebar-item sidebar-link active flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="layout-dashboard" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Dashboard</span>
                    <span class="tooltip">Dashboard</span>
                </a>
                <a href="#" data-target="orders" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="shopping-cart" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Orders</span>
                    <span class="sidebar-badge ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">8</span>
                    <span class="tooltip">Orders</span>
                </a>
                <a href="#" data-target="tables" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="layout" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Tables</span>
                    <span class="sidebar-badge ml-auto bg-green-100 text-green-600 text-xs font-medium px-2 py-1 rounded-full">12/16</span>
                    <span class="tooltip">Tables</span>
                </a>
                <a href="#" data-target="tasks" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="clipboard-check" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Tasks</span>
                    <span class="sidebar-badge ml-auto bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full">5</span>
                    <span class="tooltip">Tasks</span>
                </a>
                <a href="#" data-target="customers" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="users" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Customers</span>
                    <span class="tooltip">Customers</span>
                </a>
                <a href="#" data-target="inventory" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="package" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Inventory</span>
                    <span class="sidebar-badge ml-auto bg-orange-100 text-orange-600 text-xs font-medium px-2 py-1 rounded-full">!</span>
                    <span class="tooltip">Inventory</span>
                </a>
                <a href="#" data-target="analytics" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                    <i data-lucide="bar-chart-2" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                    <span class="sidebar-link-text font-medium">Analytics</span>
                    <span class="tooltip">Analytics</span>
                </a>
            </nav>
            
            <div class="flex-shrink-0">
                <div class="px-3 my-4 pro-plan-banner">
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                        <div class="flex items-center gap-3 mb-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i data-lucide="zap" class="w-4 h-4 text-blue-600"></i>
                            </div>
                            <div>
                                <p class="text-sm font-semibold text-slate-800">Pro Plan</p>
                                <p class="text-xs text-slate-500">Upgrade for more features</p>
                            </div>
                        </div>
                        <button class="w-full bg-blue-600 text-white text-xs font-medium py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            Upgrade Now
                        </button>
                    </div>
                </div>

                <div class="mb-2 px-3">
                    <a href="#" data-target="settings" class="sidebar-item sidebar-link flex items-center py-3 px-4 rounded-xl text-slate-700">
                        <i data-lucide="settings" class="w-5 h-5 mr-3 flex-shrink-0"></i>
                        <span class="sidebar-link-text font-medium">Settings</span>
                         <span class="tooltip">Settings</span>
                    </a>
                    <button id="sidebar-toggle" class="hidden lg:flex items-center justify-center w-full py-3 text-slate-500 hover:bg-slate-100 rounded-lg mt-2">
                        <i data-lucide="chevrons-left" class="w-5 h-5 transition-transform duration-300"></i>
                    </button>
                </div>
            </div>
        </aside>

        <div id="main-content-wrapper" class="flex-1 flex flex-col overflow-x-hidden">
            <header class="flex justify-between items-center p-4 sm:p-6 bg-white border-b">
                <div class="flex items-center">
                    <button id="mobile-menu-button" class="lg:hidden mr-4 text-slate-600 hover:text-slate-900">
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h2 id="header-title" class="text-2xl font-semibold text-slate-800">Dashboard</h2>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="user-role-badge" class="hidden lg:flex items-center gap-2 px-3 py-2 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                        <span id="role-icon">👨‍💼</span>
                        <span id="role-text">Admin</span>
                    </div>

                    <button class="text-slate-600 hover:text-blue-600"><i data-lucide="bell" class="w-6 h-6"></i></button>

                    <div class="relative">
                        <button id="user-menu-btn" class="flex items-center gap-2 text-slate-600 hover:text-slate-800 transition-colors">
                            <img id="user-avatar" class="h-10 w-10 rounded-full object-cover" src="https://placehold.co/100x100/E2E8F0/475569?text=AV" alt="User avatar">
                            <span class="absolute right-0 bottom-0 h-3 w-3 bg-green-500 rounded-full border-2 border-white"></span>
                            <i data-lucide="chevron-down" class="w-4 h-4"></i>
                        </button>

                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-slate-200 py-2 z-50">
                            <div class="px-4 py-2 border-b border-slate-100">
                                <p id="user-name" class="font-semibold text-slate-800">User Name</p>
                                <p id="user-email" class="text-sm text-slate-500"><EMAIL></p>
                            </div>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="user" class="w-4 h-4"></i>
                                Profile
                            </a>
                            <a href="#" class="flex items-center gap-2 px-4 py-2 text-slate-600 hover:bg-slate-50 transition-colors">
                                <i data-lucide="settings" class="w-4 h-4"></i>
                                Settings
                            </a>
                            <hr class="my-2">
                            <button id="logout-btn" class="flex items-center gap-2 w-full px-4 py-2 text-red-600 hover:bg-red-50 transition-colors">
                                <i data-lucide="log-out" class="w-4 h-4"></i>
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gradient-to-br from-slate-50 to-blue-50 p-4 sm:p-6 lg:p-8">
                <div id="welcome-banner" class="hidden mb-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-bold mb-2">🎉 Welcome to RestroManager!</h3>
                            <p class="text-blue-100">Hi <span id="user-name-banner"></span>! Your account has been successfully created. Let's get your restaurant set up!</p>
                        </div>
                        <button id="dismiss-welcome" class="text-blue-200 hover:text-white transition-colors">
                            <i data-lucide="x" class="w-6 h-6"></i>
                        </button>
                    </div>
                    <div class="mt-4 flex flex-wrap gap-3">
                        <button class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors">
                            Complete Setup
                        </button>
                        <button class="bg-blue-400 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-300 transition-colors">
                            Watch Tutorial
                        </button>
                    </div>
                </div>

                <div id="dashboard-content" class="content-section active">
                    <div class="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.1s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Today's Revenue</span>
                                    <span class="text-2xl font-bold text-slate-800">₹24,589</span>
                                </div>
                                <div class="p-2 bg-blue-50 rounded-lg">
                                    <i data-lucide="trending-up" class="w-6 h-6 text-blue-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>12.5%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.2s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Active Orders</span>
                                    <span class="text-2xl font-bold text-slate-800">18</span>
                                </div>
                                <div class="p-2 bg-green-50 rounded-lg">
                                    <i data-lucide="clipboard-list" class="w-6 h-6 text-green-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>8.2%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last hour</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.3s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Table Occupancy</span>
                                    <span class="text-2xl font-bold text-slate-800">76%</span>
                                </div>
                                <div class="p-2 bg-orange-50 rounded-lg">
                                    <i data-lucide="utensils" class="w-6 h-6 text-orange-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-neutral">
                                    <i data-lucide="minus" class="w-3 h-3"></i>
                                    <span>2.1%</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. yesterday</span>
                            </div>
                        </div>

                        <div class="stat-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.4s;">
                            <div class="flex justify-between items-start mb-4">
                                <div class="flex flex-col">
                                    <span class="text-sm font-medium text-slate-500">Customer Rating</span>
                                    <span class="text-2xl font-bold text-slate-800">4.8/5</span>
                                </div>
                                <div class="p-2 bg-purple-50 rounded-lg">
                                    <i data-lucide="star" class="w-6 h-6 text-purple-600"></i>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="metric-trend trend-up">
                                    <i data-lucide="arrow-up-right" class="w-3 h-3"></i>
                                    <span>0.3</span>
                                </div>
                                <span class="text-xs text-slate-500">vs. last week</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <div class="lg:col-span-2 glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.5s;">
                            <div class="flex justify-between items-center mb-6">
                                <div>
                                    <h3 class="text-lg font-semibold text-slate-800">Revenue Analytics</h3>
                                    <p class="text-sm text-slate-500">Last 7 days performance</p>
                                </div>
                                <div class="flex gap-2">
                                    <button class="px-3 py-1 text-xs font-medium bg-blue-100 text-blue-700 rounded-lg">7D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">30D</button>
                                    <button class="px-3 py-1 text-xs font-medium text-slate-500 hover:bg-slate-100 rounded-lg">90D</button>
                                </div>
                            </div>
                            <div class="h-64 flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl">
                                <div class="text-center">
                                    <i data-lucide="bar-chart-3" class="w-12 h-12 text-blue-400 mx-auto mb-2"></i>
                                    <p class="text-sm text-slate-500">Revenue chart will be displayed here</p>
                                </div>
                            </div>
                        </div>

                        <div class="glass-card rounded-2xl p-6 shadow-lg animate-slide-in" style="animation-delay: 0.6s;">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold text-slate-800">Top Dishes</h3>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">View All</button>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍕</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Margherita Pizza</p>
                                            <p class="text-xs text-slate-500">42 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-green-600">₹1,680</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍝</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Pasta Alfredo</p>
                                            <p class="text-xs text-slate-500">38 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-blue-600">₹1,520</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🍔</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Classic Burger</p>
                                            <p class="text-xs text-slate-500">35 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-orange-600">₹1,400</span>
                                </div>

                                <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <span class="text-lg">🥗</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-slate-800">Caesar Salad</p>
                                            <p class="text-xs text-slate-500">28 orders</p>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-purple-600">₹1,120</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div id="orders-content" class="content-section">
                    <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-3">
                                <div class="p-2 bg-blue-100 rounded-xl">
                                    <i data-lucide="receipt" class="w-6 h-6 text-blue-600"></i>
                                </div>
                                <div>
                                    <h1 class="text-2xl font-bold text-slate-900">Orders Management</h1>
                                    <p class="text-slate-600 text-sm">Real-time order tracking and management</p>
                                </div>
                            </div>
                            <div class="flex items-center gap-4 text-sm text-slate-500">
                                <span class="flex items-center gap-1">
                                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    Live updates
                                </span>
                                <span>Last updated: Just now</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3 mt-4 lg:mt-0">
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-200 font-medium shadow-lg shadow-blue-500/25">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                New Order
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="download" class="w-4 h-4"></i>
                                Export
                            </button>
                            <button class="flex items-center gap-2 px-4 py-2.5 bg-white border border-slate-300 text-slate-700 rounded-xl hover:bg-slate-50 hover:border-slate-400 transition-all duration-200 font-medium">
                                <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-blue-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="shopping-cart" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +12%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Orders Today</p>
                                <p class="text-3xl font-bold text-slate-900 mb-1">247</p>
                                <p class="text-xs text-slate-600">vs 221 yesterday</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-orange-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-orange-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                                        Urgent
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Pending Orders</p>
                                <p class="text-3xl font-bold text-orange-600 mb-1">8</p>
                                <p class="text-xs text-slate-600">Awaiting confirmation</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-purple-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-purple-600 text-sm font-medium">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                                        Active
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">In Kitchen</p>
                                <p class="text-3xl font-bold text-purple-600 mb-1">15</p>
                                <p class="text-xs text-slate-600">Being prepared</p>
                            </div>
                        </div>

                        <div class="group bg-white rounded-2xl p-6 shadow-sm border border-slate-200 hover:shadow-lg hover:border-green-300 transition-all duration-300 cursor-pointer">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-green-600 text-sm font-medium">
                                        <i data-lucide="trending-up" class="w-4 h-4"></i>
                                        +8%
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Completed Today</p>
                                <p class="text-3xl font-bold text-green-600 mb-1">224</p>
                                <p class="text-xs text-slate-600">₹18,450 revenue</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl p-4 shadow-sm border border-slate-200 mb-6">
                        <div class="mb-4">
                            <div class="relative">
                                <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400"></i>
                                <input type="text" id="order-search" placeholder="Search orders..."
                                       class="w-full pl-10 pr-4 py-2.5 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button class="order-filter-btn active px-3 py-1.5 bg-blue-600 text-white rounded-lg text-xs font-medium" data-status="all">
                                All (247)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-orange-100 text-orange-700 rounded-lg text-xs font-medium hover:bg-orange-200" data-status="pending">
                                Pending (8)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-purple-100 text-purple-700 rounded-lg text-xs font-medium hover:bg-purple-200" data-status="preparing">
                                Kitchen (15)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-lg text-xs font-medium hover:bg-yellow-200" data-status="ready">
                                Ready (5)
                            </button>
                            <button class="order-filter-btn px-3 py-1.5 bg-green-100 text-green-700 rounded-lg text-xs font-medium hover:bg-green-200" data-status="completed">
                                Done (224)
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                        <div class="px-4 py-3 border-b border-slate-200 bg-slate-50">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-slate-700">Recent Orders</span>
                                <span class="text-xs text-slate-500">10 of 247</span>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-slate-50 border-b border-slate-200">
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Order</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Customer</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden sm:table-cell">Table</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Items</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Total</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Status</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs hidden lg:table-cell">Time</th>
                                        <th class="text-left py-3 px-4 font-medium text-slate-700 text-xs">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="orders-table-body" class="divide-y divide-slate-100">
                                    <tr class="hover:bg-slate-50 transition-colors" data-status="pending">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-001</div>
                                            <div class="text-xs text-slate-500">2 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">John Smith</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 5</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">2x Burger, 1x Fries</div>
                                            <div class="text-xs text-blue-600">+1 more</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹850</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                Pending
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:34 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex gap-1">
                                                <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                    Accept
                                                </button>
                                                <button class="px-2 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700">
                                                    Reject
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="preparing">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-002</div>
                                            <div class="text-xs text-slate-500">8 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Sarah Johnson</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 3</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pizza, 2x Coke</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹1,200</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                Kitchen
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:26 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700">
                                                Mark Ready
                                            </button>
                                        </td>
                                    </tr>

                                    <tr class="hover:bg-slate-50 transition-colors" data-status="ready">
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">#ORD-003</div>
                                            <div class="text-xs text-slate-500">12 min ago</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-medium text-slate-900 text-sm">Mike Davis</div>
                                            <div class="text-xs text-slate-500">+****************</div>
                                        </td>
                                        <td class="py-3 px-4 hidden sm:table-cell">
                                            <span class="font-medium text-slate-900 text-sm">Table 7</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="text-sm text-slate-900">1x Pasta, 1x Salad</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="font-bold text-slate-900">₹950</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                Ready
                                            </span>
                                        </td>
                                        <td class="py-3 px-4 hidden lg:table-cell">
                                            <div class="text-sm text-slate-900">2:22 PM</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="px-2 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                Serve
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="bg-slate-50 px-4 py-3 border-t border-slate-200">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-slate-600">
                                    Showing 1-10 of 247 orders
                                </div>
                                <div class="flex items-center gap-2">
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white" disabled>
                                        Previous
                                    </button>
                                    <button class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded">1</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">2</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">3</button>
                                    <button class="px-3 py-1.5 text-sm border border-slate-300 rounded hover:bg-white">
                                        Next
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tasks-content" class="content-section">
                    <div class="bg-white rounded-2xl p-6 shadow-sm border border-slate-200 mb-8">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                                    <i data-lucide="kanban-square" class="w-5 h-5 text-white"></i>
                                </div>
                                <div>
                                    <h1 class="text-xl font-bold text-slate-900 mb-1">Task Management</h1>
                                    <p class="text-sm text-slate-500">Organize, assign, and track team tasks efficiently</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-3">
                                <button id="add-task-btn" class="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium text-sm">
                                    <i data-lucide="plus" class="w-4 h-4"></i>
                                    Add Task
                                </button>
                                <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                    <i data-lucide="filter" class="w-4 h-4"></i>
                                    Filter
                                </button>
                                <button class="flex items-center gap-2 px-4 py-2 bg-white border border-slate-300 text-slate-600 rounded-lg hover:bg-slate-50 transition-colors font-medium text-sm">
                                    <i data-lucide="users" class="w-4 h-4"></i>
                                    Team
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center gap-6 mt-4 pt-4 border-t border-slate-100">
                            <div class="flex items-center gap-2 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-slate-600">Real-time updates</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <span class="font-medium text-slate-900">5</span>
                                <span class="text-slate-600">active tasks</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <span class="font-medium text-slate-900">3</span>
                                <span class="text-slate-600">team members</span>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg">
                                    <i data-lucide="list-todo" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full">+2 today</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Total Tasks</p>
                                <p class="text-3xl font-bold text-slate-900">12</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg">
                                    <i data-lucide="clock" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-orange-600 bg-orange-100 px-2 py-1 rounded-full">Pending</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">To Do</p>
                                <p class="text-3xl font-bold text-orange-600">5</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
                                    <i data-lucide="play-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-full">Active</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">In Progress</p>
                                <p class="text-3xl font-bold text-purple-600">3</p>
                            </div>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-sm border border-slate-200 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-between mb-4">
                                <div class="p-3 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                                    <i data-lucide="check-circle" class="w-6 h-6 text-white"></i>
                                </div>
                                <span class="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">+4 today</span>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-500 mb-1">Completed</p>
                                <p class="text-3xl font-bold text-green-600">4</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div id="all-tasks" class="tasks-container space-y-3">
                            <div id="task-1" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                        <span class="text-xs font-semibold text-red-600 bg-red-50 px-2.5 py-1 rounded-md border border-red-200">HIGH</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Clean the main dining area</h5>
                                        <p class="text-sm text-slate-600 line-clamp-1">Wipe down all tables, chairs, and sanitize surfaces before opening.</p>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/FEE2E2/B91C1C?text=SM" alt="Sarah Miller">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>30m</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-md border border-blue-200">Cleaning</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-2" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="todo">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                                        <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Restock bar supplies</h5>
                                        <p class="text-sm text-slate-600 line-clamp-1">Check inventory for lemons, limes, and other bar essentials.</p>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/D1FAE5/047857?text=KW" alt="Kevin Wang">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>45m</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-indigo-700 bg-indigo-50 rounded-md border border-indigo-200">Inventory</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-3" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="inprogress">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-purple-500 rounded-full animate-pulse"></div>
                                        <span class="text-xs font-semibold text-purple-600 bg-purple-50 px-2.5 py-1 rounded-md border border-purple-200">IN PROGRESS</span>
                                        <span class="text-xs font-semibold text-yellow-600 bg-yellow-50 px-2.5 py-1 rounded-md border border-yellow-200">MED</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Prepare daily specials</h5>
                                        <div class="flex items-center gap-3">
                                            <p class="text-sm text-slate-600 line-clamp-1 flex-1">Prep ingredients for the Chef's special pasta dish.</p>
                                            <div class="flex items-center gap-2">
                                                <div class="w-20 bg-slate-200 rounded-full h-1.5">
                                                    <div class="bg-purple-500 h-1.5 rounded-full transition-all duration-300" style="width: 65%"></div>
                                                </div>
                                                <span class="text-xs text-purple-600 font-semibold">65%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex -space-x-1">
                                            <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/EEF2FF/4338CA?text=EB" alt="Emily Brown">
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <div class="flex items-center gap-1 text-xs text-slate-500 bg-slate-50 px-2 py-1 rounded border">
                                                <i data-lucide="clock" class="w-3 h-3"></i>
                                                <span>20m left</span>
                                            </div>
                                            <span class="px-2.5 py-1 text-xs font-medium text-amber-700 bg-amber-50 rounded-md border border-amber-200">Kitchen</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div id="task-4" class="task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm" draggable="true" data-status="done">
                                <div class="flex items-center gap-4">
                                    <div class="flex items-center gap-3 flex-shrink-0">
                                        <div class="w-1 h-6 bg-green-500 rounded-full"></div>
                                        <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">DONE</span>
                                        <span class="text-xs font-semibold text-green-600 bg-green-50 px-2.5 py-1 rounded-md border border-green-200">LOW</span>
                                    </div>

                                    <div class="flex-1 min-w-0">
                                        <h5 class="font-semibold text-slate-900 mb-1 truncate">Set up outdoor seating</h5>
                                        <div class="flex items-center gap-3">
                                            <p class="text-sm text-slate-600 line-clamp-1 flex-1">Arrange tables and umbrellas on the patio.</p>
                                            <div class="flex items-center gap-2 text-xs">
                                                <i data-lucide="check-circle" class="w-3 h-3 text-green-600"></i>
                                                <span class="text-green-700 font-medium">2h ago</span>
                                                <span class="text-slate-300">•</span>
                                                <span class="text-green-600 font-medium">On time</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-4 flex-shrink-0">
                                        <div class="flex items-center gap-2">
                                            <div class="flex -space-x-1">
                                                <img class="h-6 w-6 rounded-full object-cover border-2 border-white shadow-sm" src="https://placehold.co/100x100/E0F2FE/0891B2?text=JD" alt="John Doe">
                                            </div>
                                            <span class="text-xs text-slate-500 font-medium">John</span>
                                        </div>

                                        <div class="flex items-center gap-3">
                                            <span class="px-2.5 py-1 text-xs font-medium text-green-700 bg-green-50 rounded-md border border-green-200">Setup</span>
                                        </div>

                                        <button class="opacity-0 group-hover:opacity-100 p-1.5 hover:bg-slate-100 rounded-lg transition-all">
                                            <i data-lucide="more-horizontal" class="w-4 h-4 text-slate-400"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <button class="w-full p-4 border-2 border-dashed border-slate-300 rounded-lg text-slate-500 hover:border-purple-300 hover:text-purple-600 hover:bg-purple-50 transition-all duration-200 flex items-center justify-center gap-2">
                                <i data-lucide="plus" class="w-4 h-4"></i>
                                Add new task
                            </button>
                        </div>
                    </div>
                </div>

                <div id="tables-content" class="content-section">
                     <p class="p-6">Table management will be here.</p>
                </div>
                
                <div id="customers-content" class="content-section">
                     <p class="p-6">Customer management will be here.</p>
                </div>

                <div id="inventory-content" class="content-section">
                     <p class="p-6">Inventory management will be here.</p>
                </div>

                <div id="analytics-content" class="content-section">
                     <p class="p-6">Analytics charts will be here.</p>
                </div>

                <div id="settings-content" class="content-section">
                     <p class="p-6">Settings page will be here.</p>
                </div>
            </main>
        </div>
    </div>

    <div id="add-task-modal" class="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md m-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-slate-800">Add a New Task</h3>
                <button id="close-modal-btn" class="text-slate-500 hover:text-slate-800">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>
            <form id="add-task-form">
                <div class="space-y-4">
                    <div>
                        <label for="task-title" class="block text-sm font-medium text-slate-700">Title</label>
                        <input type="text" id="task-title" name="title" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label for="task-description" class="block text-sm font-medium text-slate-700">Description</label>
                        <textarea id="task-description" name="description" rows="3" class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                    <div>
                        <label for="task-category" class="block text-sm font-medium text-slate-700">Category</label>
                        <input type="text" id="task-category" name="category" required class="mt-1 block w-full px-3 py-2 border border-slate-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Cleaning, Inventory">
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" id="cancel-modal-btn" class="px-4 py-2 text-sm font-medium text-slate-700 bg-slate-100 rounded-md hover:bg-slate-200">Cancel</button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">Save Task</button>
                </div>
            </form>
        </div>
    </div>


    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Lucide Icons
            lucide.createIcons();
            
            // Check authentication and load user data
            const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');

            // Redirect to login if not authenticated
            if (!userData.email || !userData.isLoggedIn) {
                window.location.href = './login.html';
                return;
            }

            // User interface setup function
            function setupUserInterface(userData) {
                const roleIcons = {
                    admin: '👨‍💼',
                    manager: '👩‍💼',
                    staff: '👨‍🍳',
                    waiter: '🧑‍🍳'
                };

                const roleNames = {
                    admin: 'Admin',
                    manager: 'Manager',
                    staff: 'Staff',
                    waiter: 'Waiter'
                };

                // Update role badge
                const roleIcon = document.getElementById('role-icon');
                const roleText = document.getElementById('role-text');
                const roleBadge = document.getElementById('user-role-badge');

                if (roleIcon && roleText && userData.role) {
                    roleIcon.textContent = roleIcons[userData.role] || '👤';
                    roleText.textContent = roleNames[userData.role] || userData.role;
                    roleBadge.classList.remove('hidden');
                }

                // Update user menu
                const userNameEl = document.querySelector('#user-menu #user-name');
                const userEmailEl = document.querySelector('#user-menu #user-email');
                const userAvatar = document.getElementById('user-avatar');

                if (userNameEl) userNameEl.textContent = userData.name || 'User';
                if (userEmailEl) userEmailEl.textContent = userData.email || '';

                // Update avatar with role-based initials
                if (userAvatar) {
                    const initials = (userData.name || userData.role || 'U').substring(0, 2).toUpperCase();
                    userAvatar.src = `https://placehold.co/100x100/2563eb/ffffff?text=${initials}`;
                }

                // Setup user menu toggle
                const userMenuBtn = document.getElementById('user-menu-btn');
                const userMenu = document.getElementById('user-menu');

                if (userMenuBtn && userMenu) {
                    userMenuBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        userMenu.classList.toggle('hidden');
                    });

                    // Close menu when clicking outside
                    document.addEventListener('click', () => {
                        userMenu.classList.add('hidden');
                    });

                    userMenu.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });
                }
                
                // Setup logout functionality
                const logoutBtn = document.getElementById('logout-btn');
                if (logoutBtn) {
                    logoutBtn.addEventListener('click', () => {
                        const confirmed = confirm('Are you sure you want to logout?');
                        if (confirmed) {
                            const userData = JSON.parse(localStorage.getItem('restroManagerUser') || '{}');
                            userData.isLoggedIn = false;
                            localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                            window.location.href = './login.html';
                        }
                    });
                }
            }
            
            setupUserInterface(userData);
            
            // Check for new user and show welcome banner
            const welcomeBanner = document.getElementById('welcome-banner');
            const userNameSpan = document.getElementById('user-name-banner');
            const dismissWelcome = document.getElementById('dismiss-welcome');

            if (userData.isNewUser && userData.firstName) {
                welcomeBanner.classList.remove('hidden');
                userNameSpan.textContent = userData.firstName;

                dismissWelcome.addEventListener('click', () => {
                    welcomeBanner.classList.add('hidden');
                    userData.isNewUser = false;
                    localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                });
            }

            // --- DOM Elements ---
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebarBackdrop = document.getElementById('sidebar-backdrop');
            
            const sidebarLinks = document.querySelectorAll('.sidebar-item');
            const contentSections = document.querySelectorAll('.content-section');
            const headerTitle = document.getElementById('header-title');
            
            const SIDEBAR_STATE_KEY = 'sidebarCollapsed';

            // --- Sidebar Logic ---
            const applySidebarState = (isCollapsed) => {
                sidebar.classList.toggle('collapsed', isCollapsed);
            };

            const toggleDesktopSidebar = () => {
                const isCollapsed = !sidebar.classList.contains('collapsed');
                applySidebarState(isCollapsed);
                localStorage.setItem(SIDEBAR_STATE_KEY, isCollapsed.toString());
            };

            if(sidebarToggle) sidebarToggle.addEventListener('click', toggleDesktopSidebar);

            const toggleMobileSidebar = () => {
                sidebar.classList.toggle('open');
                sidebarBackdrop.classList.toggle('hidden');
            };

            if(mobileMenuButton) mobileMenuButton.addEventListener('click', toggleMobileSidebar);
            if(sidebarBackdrop) sidebarBackdrop.addEventListener('click', toggleMobileSidebar);

            const initSidebar = () => {
                const isDesktop = window.innerWidth >= 1024;
                if (isDesktop) {
                    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY) === 'true';
                    applySidebarState(savedState);
                    sidebar.classList.remove('open');
                    sidebarBackdrop.classList.add('hidden');
                } else {
                    sidebar.classList.add('open');
                    sidebar.classList.remove('collapsed');
                }
            };
            
            // --- Navigation Logic ---
            sidebarLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    const targetId = e.currentTarget.dataset.target;
                    const targetContent = document.getElementById(targetId + '-content');
                    const linkTextSpan = e.currentTarget.querySelector('.sidebar-link-text');

                    if (linkTextSpan) {
                         const linkText = linkTextSpan.textContent;
                         headerTitle.textContent = linkText;
                    }


                    sidebarLinks.forEach(l => l.classList.remove('active'));
                    e.currentTarget.classList.add('active');


                    contentSections.forEach(section => section.classList.remove('active'));
                    if(targetContent) {
                        targetContent.classList.add('active');
                    }

                    if (window.innerWidth < 1024 && sidebar.classList.contains('open')) {
                        toggleMobileSidebar();
                    }
                });
            });

            // --- Task Board Logic ---
            const addTaskBtn = document.getElementById('add-task-btn');
            const addTaskModal = document.getElementById('add-task-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const cancelModalBtn = document.getElementById('cancel-modal-btn');
            const addTaskForm = document.getElementById('add-task-form');
            const allTasksContainer = document.getElementById('all-tasks');
            
            const openTaskModal = () => addTaskModal.classList.remove('hidden');
            const closeTaskModal = () => addTaskModal.classList.add('hidden');

            if (addTaskBtn) addTaskBtn.addEventListener('click', openTaskModal);
            if (closeModalBtn) closeModalBtn.addEventListener('click', closeTaskModal);
            if (cancelModalBtn) cancelModalBtn.addEventListener('click', closeTaskModal);

            if (addTaskForm) {
                addTaskForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    const title = e.target.title.value;
                    const description = e.target.description.value;
                    const category = e.target.category.value;
                    
                    if (!title || !category) return;

                    const newTaskCard = document.createElement('div');
                    newTaskCard.className = 'task-card bg-white p-4 cursor-pointer hover:bg-slate-50 transition-all duration-200 group rounded-lg border border-slate-200 shadow-sm';
                    newTaskCard.setAttribute('draggable', 'true');
                    newTaskCard.innerHTML = `
                        <div class="flex items-center gap-4">
                            <div class="flex items-center gap-3 flex-shrink-0">
                                <div class="w-1 h-6 bg-orange-500 rounded-full"></div>
                                <span class="text-xs font-semibold text-orange-600 bg-orange-50 px-2.5 py-1 rounded-md border border-orange-200">TO DO</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h5 class="font-semibold text-slate-900 mb-1 truncate">${title}</h5>
                                <p class="text-sm text-slate-600 line-clamp-1">${description}</p>
                            </div>
                            <div class="flex items-center gap-3">
                                <span class="px-2.5 py-1 text-xs font-medium text-blue-700 bg-blue-50 rounded-md border border-blue-200">${category}</span>
                            </div>
                        </div>
                    `;
                    
                    const addTaskButton = allTasksContainer.querySelector('button');
                    allTasksContainer.insertBefore(newTaskCard, addTaskButton);

                    e.target.reset();
                    closeTaskModal();
                });
            }

            // --- Initializations ---
            initSidebar();
            window.addEventListener('resize', initSidebar);
        });
    </script>
</body>
</html>