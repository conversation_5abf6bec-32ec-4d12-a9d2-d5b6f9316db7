<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RestroManager - Restaurant Management System</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Icons - with fallbacks -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 32 32%22><rect width=%2232%22 height=%2232%22 fill=%22%232563eb%22/><text x=%2216%22 y=%2220%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2212%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 16 16%22><rect width=%2216%22 height=%2216%22 fill=%22%232563eb%22/><text x=%228%22 y=%2212%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%228%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 180 180%22><rect width=%22180%22 height=%22180%22 fill=%22%232563eb%22/><text x=%2290%22 y=%22110%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2260%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="mask-icon" href="/icons/icon-192x192.png" color="#2563eb">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
