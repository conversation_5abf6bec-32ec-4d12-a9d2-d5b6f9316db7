import React, { useState, useEffect } from 'react';
import { ChefHat, Mail, Lock, Eye, EyeOff, Store, Utensils, ArrowLeft, Plus, X, User, Phone, MapPin, Building, MailOpen, UserCheck, Clock, Layout as LayoutIcon, Check, Trash2, ChevronDown } from 'lucide-react';

// --- TYPE DEFINITIONS --- //

type StepId = 'account' | 'business' | 'operations' | 'tables' | 'summary' | 'personal';

interface StepConfig {
    id: StepId;
    label: string;
}

interface Staff {
    id: number;
    name: string;
    count: number;
    category: 'Kitchen' | 'Front of House';
    isCustom: boolean;
}

interface Service {
    name: 'Lunch' | 'Dinner';
    isActive: boolean;
    startTime: string;
    endTime: string;
    averageTill: string;
    staff: Staff[];
}

interface DaySchedule {
    dayName: string;
    isClosed: boolean;
    openingTime: string;
    closingTime: string;
    services: Service[];
}

interface Table {
    id: number;
    name: string;
    capacity: number;
    type: 'Indoor' | 'Outdoor' | 'Bar';
}

interface TableSection {
    id: number;
    name: string;
    tables: Table[];
}

// --- SVG ICONS (from user) --- //
const LayoutGridIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
);

const Move3dIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M5 12h14"/><path d="M12 5v14"/><path d="M18 15l3-3-3-3"/><path d="M6 9l-3 3 3 3"/><path d="M15 18l-3 3-3-3"/><path d="M9 6l3-3 3 3"/></svg>
);


// --- UTILITY & DATA (TOP-LEVEL SCOPE) --- //

const STEP_CONFIG: StepConfig[] = [
    { id: 'account', label: 'Account' },
    { id: 'business', label: 'Business & Cuisine' },
    { id: 'operations', label: 'Operations' },
    { id: 'tables', label: 'Tables' },
    { id: 'summary', label: 'Review' },
    { id: 'personal', label: 'Personal' }
];

const DAYS_OF_WEEK = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];

const createInitialStaffing = (): Staff[] => [
    { id: 1, name: 'Head Chef', count: 0, category: 'Kitchen', isCustom: false },
    { id: 2, name: 'Sous Chef', count: 0, category: 'Kitchen', isCustom: false },
    { id: 3, name: 'Line Chefs', count: 0, category: 'Kitchen', isCustom: false },
    { id: 4, name: 'Managers', count: 0, category: 'Front of House', isCustom: false },
    { id: 5, name: 'Waiters', count: 0, category: 'Front of House', isCustom: false },
    { id: 6, name: 'Food Runners', count: 0, category: 'Front of House', isCustom: false },
    { id: 7, name: 'Bartenders', count: 0, category: 'Front of House', isCustom: false },
    { id: 8, name: 'Cleaners', count: 0, category: 'Front of House', isCustom: false },
];

const createInitialDay = (dayName: string): DaySchedule => ({
    dayName,
    isClosed: dayName === 'Sunday',
    openingTime: "10:00",
    closingTime: "23:00",
    services: [
        { name: "Lunch", isActive: true, startTime: "12:00", endTime: "15:00", averageTill: '<300', staff: createInitialStaffing() },
        { name: "Dinner", isActive: true, startTime: "17:30", endTime: "22:00", averageTill: '<500', staff: createInitialStaffing() },
    ],
});

// --- HELPER & GENERIC COMPONENTS --- //

const FormInput: React.FC<{
    icon?: React.ElementType;
    label?: string;
    name: string;
    type: string;
    placeholder: string;
    value: string | number;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    required?: boolean;
    className?: string;
    children?: React.ReactNode;
    min?: number;
}> = ({ icon: Icon, label, name, type, placeholder, value, onChange, required, className, children, ...props }) => (
    <div className={`form-group ${className}`}>
        {label && <label htmlFor={name} className="block text-sm font-semibold text-slate-700 mb-2">{label}</label>}
        <div className="relative">
            {Icon && <Icon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />}
            <input
                type={type}
                name={name}
                id={name}
                required={required}
                placeholder={placeholder}
                value={value}
                onChange={onChange}
                className={`form-input ${Icon ? 'pl-12' : 'px-4'} block w-full py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
                {...props}
            />
            {children}
        </div>
    </div>
);

const StepIndicator: React.FC<{ currentStep: number }> = ({ currentStep }) => (
    <div id="step-indicators-container" className="w-full flex items-center">
        {STEP_CONFIG.map((step, index) => {
            const isCompleted = index < currentStep;
            const isActive = index === currentStep;
            return (
                 <React.Fragment key={step.id}>
                    <div className={`step-indicator flex-1 flex flex-col items-center relative ${isCompleted ? 'completed' : ''} ${isActive ? 'active' : ''}`}>
                        <div className="step-circle relative z-10 w-8 h-8 rounded-full flex items-center justify-center font-bold border-2 border-slate-300 bg-white flex-shrink-0">
                            {isCompleted ? <Check className="w-4 h-4" /> : index + 1}
                        </div>
                        <div className="step-label text-xs text-center mt-2 text-slate-500">{step.label}</div>
                    </div>
                    {index < STEP_CONFIG.length - 1 && (
                        <div className="step-connector-wrapper flex-1 relative h-8 flex items-center">
                             <div className={`step-connector h-1 w-full absolute top-1/2 -translate-y-1/2 ${isCompleted ? 'completed' : ''} ${isActive ? 'active' : ''}`}></div>
                        </div>
                    )}
                </React.Fragment>
            );
        })}
    </div>
);

const ActionButtons: React.FC<{
    onNext: () => void;
    onPrev?: () => void;
    isSubmitting?: boolean;
    nextText?: string;
    isFinalStep?: boolean;
}> = ({ onNext, onPrev, isSubmitting = false, nextText = "Next Step", isFinalStep = false }) => (
    <div className="mt-8 flex items-center gap-4">
        {onPrev && (
            <button type="button" onClick={onPrev} className="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all flex items-center justify-center">
                <ArrowLeft className="w-4 h-4 mr-2" /> Back
            </button>
        )}
        <button type={isFinalStep ? "submit" : "button"} onClick={!isFinalStep ? onNext : undefined} disabled={isSubmitting} className="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-slate-400">
            {isSubmitting ? (
                <span className="btn-loading flex items-center justify-center gap-2">
                    <span className="loading-spinner"></span> Processing...
                </span>
            ) : (
                <span className="btn-text">{isFinalStep ? "Create Account & Finish" : nextText}</span>
            )}
        </button>
    </div>
);

// --- STEP-SPECIFIC COMPONENTS --- //

const Step1Account: React.FC<{ formData: any, handleChange: any, passwordStrength: any, setPasswordStrength: any }> = ({ formData, handleChange, passwordStrength, setPasswordStrength }) => {
    const [showPassword, setShowPassword] = useState(false);

    useEffect(() => {
        const password = formData.password;
        if (!password) {
            setPasswordStrength({ score: 0, label: '' });
            return;
        }
        let strength = 0;
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        const labels = { 1: 'Very Weak', 2: 'Weak', 3: 'Fair', 4: 'Good', 5: 'Strong' };
        setPasswordStrength({ score: strength, label: (labels as any)[strength] || '' });
    }, [formData.password, setPasswordStrength]);

    const strengthColors: { [key: number]: string } = { 1: 'bg-red-400', 2: 'bg-red-400', 3: 'bg-yellow-400', 4: 'bg-blue-400', 5: 'bg-green-400' };

    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Create your account</h2>
                <p className="text-slate-500">Start by setting up your login details.</p>
            </div>
            <div className="space-y-6">
                 <button type="button" className="w-full flex items-center justify-center gap-3 py-4 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group">
                    <img src="https://www.google.com/favicon.ico" alt="Google icon" className="w-5 h-5 group-hover:scale-110 transition-transform" />
                    <span className="text-sm font-medium text-slate-700">Sign up with Google</span>
                </button>
                <div className="flex items-center">
                    <div className="flex-grow border-t border-slate-200"></div>
                    <span className="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                    <div className="flex-grow border-t border-slate-200"></div>
                </div>
                <FormInput icon={Mail} name="email" type="email" placeholder="Email address" value={formData.email} onChange={handleChange} required />
                <div className="form-group">
                    <div className="relative">
                        <Lock className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                        <input
                            type={showPassword ? 'text' : 'password'}
                            name="password"
                            id="password"
                            required
                            placeholder="Password (min. 8 characters)"
                            value={formData.password}
                            onChange={handleChange}
                            className="form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button type="button" onClick={() => setShowPassword(!showPassword)} className="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                            {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                        </button>
                    </div>
                    {passwordStrength.score > 0 && (
                        <div className="password-strength mt-2">
                            <div className="flex gap-1 mb-1">
                                {Array.from({ length: 5 }).map((_, i) => (
                                    <div key={i} className={`strength-bar h-1 rounded flex-1 ${i < passwordStrength.score ? strengthColors[passwordStrength.score] : 'bg-slate-200'}`}></div>
                                ))}
                            </div>
                            <p className="strength-text text-xs text-slate-500">Password strength: {passwordStrength.label}</p>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

const Step2Business: React.FC<{ formData: any, handleChange: any }> = ({ formData, handleChange }) => {
    const cuisines = [
        { value: 'British', label: 'British', icon: '🇬🇧' },
        { value: 'Indian', label: 'Indian', icon: '🍛' },
        { value: 'Italian', label: 'Italian', icon: '🍝' },
        { value: 'Chinese', label: 'Chinese', icon: '🥢' },
        { value: 'American', label: 'American', icon: '🍔' },
        { value: 'Other', label: 'Other', icon: '🍽️' },
    ];

    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Business & Cuisine</h2>
                <p className="text-slate-500">Tell us about your business and what you serve.</p>
            </div>
            <div className="space-y-6">
                <FormInput icon={Store} label="Business Name" name="businessName" type="text" placeholder="Enter your business name" value={formData.businessName} onChange={handleChange} required />
                <div className="form-group">
                    <label htmlFor="businessType" className="block text-sm font-semibold text-slate-700 mb-2">Business Type</label>
                    <div className="relative">
                        <Utensils className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 pointer-events-none" />
                        <select id="businessType" name="businessType" value={formData.businessType} onChange={handleChange} className="form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Select business type</option>
                            <option value="Fine Dining">🍽️ Fine Dining</option>
                            <option value="Casual Dining">🍕 Casual Dining</option>
                            <option value="Cafe / Bistro">☕ Cafe / Bistro</option>
                            <option value="Quick Service (QSR)">🍔 Quick Service (QSR)</option>
                            <option value="Food Truck">🚚 Food Truck</option>
                            <option value="Pub / Bar">🍺 Pub / Bar</option>
                            <option value="Bakery">🥖 Bakery</option>
                        </select>
                    </div>
                </div>
                <div className="form-group">
                    <label className="block text-sm font-semibold text-slate-700 mb-4">Cuisine Type (select all that apply)</label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        {cuisines.map(cuisine => (
                            <div className="selectable-option" key={cuisine.value}>
                                <input type="checkbox" id={`cuisine-${cuisine.value}`} name="cuisine" value={cuisine.value} checked={formData.cuisines.includes(cuisine.value)} onChange={handleChange} className="hidden" />
                                <label htmlFor={`cuisine-${cuisine.value}`} className="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white">
                                    <div className="cuisine-icon text-2xl mb-2">{cuisine.icon}</div>
                                    <div className="text-sm">{cuisine.label}</div>
                                </label>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </>
    );
};

const Step3Operations: React.FC<{ schedule: DaySchedule[], setSchedule: React.Dispatch<React.SetStateAction<DaySchedule[]>> }> = ({ schedule, setSchedule }) => {
    const [collapsedDays, setCollapsedDays] = useState<Record<string, boolean>>({
        "Tuesday": true, "Wednesday": true, "Thursday": true, "Friday": true, "Saturday": true, "Sunday": true
    });

    const handleDayValueChange = (dayIndex: number, field: keyof DaySchedule, value: any) => {
        const updatedSchedule = [...schedule];
        (updatedSchedule[dayIndex] as any)[field] = value;
        setSchedule(updatedSchedule);
    };

    const handleServiceValueChange = (dayIndex: number, serviceIndex: number, field: keyof Service, value: any) => {
        const updatedSchedule = [...schedule];
        (updatedSchedule[dayIndex].services[serviceIndex] as any)[field] = value;
        setSchedule(updatedSchedule);
    };

    const handleStaffValueChange = (dayIndex: number, serviceIndex: number, staffId: number, field: keyof Staff, value: any) => {
        const updatedSchedule = [...schedule];
        const staffIndex = updatedSchedule[dayIndex].services[serviceIndex].staff.findIndex(s => s.id === staffId);
        (updatedSchedule[dayIndex].services[serviceIndex].staff[staffIndex] as any)[field] = value;
        setSchedule(updatedSchedule);
    };
    
    const handleAddStaff = (dayIndex: number, serviceIndex: number, category: 'Kitchen' | 'Front of House') => {
        const newRole: Staff = { id: Date.now(), name: '', count: 1, category, isCustom: true };
        const updatedSchedule = [...schedule];
        updatedSchedule[dayIndex].services[serviceIndex].staff.push(newRole);
        setSchedule(updatedSchedule);
    };

    const handleDeleteStaff = (dayIndex: number, serviceIndex: number, staffId: number) => {
        const updatedSchedule = [...schedule];
        const service = updatedSchedule[dayIndex].services[serviceIndex];
        service.staff = service.staff.filter(staff => staff.id !== staffId);
        setSchedule(updatedSchedule);
    };

    const toggleDayCollapse = (dayName: string) => {
        setCollapsedDays(prev => ({ ...prev, [dayName]: !prev[dayName] }));
    };

    const renderStaffInput = (staff: Staff, dayIndex: number, serviceIndex: number) => (
        <div key={staff.id} className="flex items-center justify-between mt-2 space-x-2 group">
            <input
                type="text"
                value={staff.name}
                placeholder="Role Name"
                readOnly={!staff.isCustom}
                onChange={(e) => handleStaffValueChange(dayIndex, serviceIndex, staff.id, 'name', e.target.value)}
                className={`staff-input flex-grow p-1.5 rounded-md text-sm bg-white border border-slate-300 ${!staff.isCustom ? 'bg-slate-100 text-slate-600' : ''}`}
            />
            <input
                type="number"
                min="0"
                value={staff.count}
                onChange={(e) => handleStaffValueChange(dayIndex, serviceIndex, staff.id, 'count', parseInt(e.target.value) || 0)}
                className="staff-input w-16 p-1.5 text-center bg-white border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400"
            />
            <div className="w-6 h-6 flex items-center justify-center">
                {staff.isCustom && (
                    <button type="button" onClick={() => handleDeleteStaff(dayIndex, serviceIndex, staff.id)} className="delete-staff-btn p-1 rounded-full hover:bg-red-100 transition-colors opacity-0 group-hover:opacity-100">
                        <Trash2 className="w-4 h-4 text-slate-400 hover:text-red-500" />
                    </button>
                )}
            </div>
        </div>
    );

    const renderService = (service: Service, dayIndex: number, serviceIndex: number, isDayClosed: boolean) => {
        const tillOptions = service.name === 'Lunch' ? ['<300', '300-600', '600-1000', '1000+'] : ['<500', '500-1000', '1000-1500', '1500-2000', '2000+'];
        const kitchenStaff = service.staff.filter(s => s.category === 'Kitchen');
        const fohStaff = service.staff.filter(s => s.category === 'Front of House');
        const isServiceActive = service.isActive && !isDayClosed;

        return (
            <div key={service.name} className={`service-editor p-4 rounded-lg mt-4 ${isServiceActive ? 'bg-white' : 'bg-slate-100 opacity-60'} transition-all`}>
                <div className="flex items-center justify-between pb-3 border-b border-slate-200">
                    <h4 className="font-semibold text-lg text-slate-700">{service.name} Service</h4>
                    <label className="flex items-center cursor-pointer">
                        <input type="checkbox" checked={service.isActive} onChange={(e) => handleServiceValueChange(dayIndex, serviceIndex, 'isActive', e.target.checked)} disabled={isDayClosed} className="service-active-toggle sr-only peer" />
                        <div className="relative w-11 h-6 bg-slate-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 peer-disabled:bg-slate-200"></div>
                    </label>
                </div>
                {isServiceActive && (
                    <div className="service-details mt-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label className="block text-sm font-medium text-slate-700 mb-1">Service Hours</label>
                                <div className="flex items-center space-x-2">
                                    <input type="time" value={service.startTime} onChange={(e) => handleServiceValueChange(dayIndex, serviceIndex, 'startTime', e.target.value)} className="service-input w-full p-2 border border-slate-300 rounded-md" />
                                    <span className="text-slate-500">-</span>
                                    <input type="time" value={service.endTime} onChange={(e) => handleServiceValueChange(dayIndex, serviceIndex, 'endTime', e.target.value)} className="service-input w-full p-2 border border-slate-300 rounded-md" />
                                </div>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-slate-700 mb-1">Average Till (£)</label>
                                <select value={service.averageTill} onChange={(e) => handleServiceValueChange(dayIndex, serviceIndex, 'averageTill', e.target.value)} className="service-input w-full p-2.5 border border-slate-300 rounded-md bg-white">
                                    {tillOptions.map(opt => <option key={opt} value={opt}>{opt}</option>)}
                                </select>
                            </div>
                        </div>
                        <div>
                            <h5 className="text-md font-semibold text-slate-800 mb-3 border-b border-slate-200 pb-2">Staffing Levels</h5>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6">
                                <div>
                                    <h6 className="font-semibold text-slate-600 mb-2">Kitchen</h6>
                                    {kitchenStaff.map(staff => renderStaffInput(staff, dayIndex, serviceIndex))}
                                    <button type="button" onClick={() => handleAddStaff(dayIndex, serviceIndex, 'Kitchen')} className="add-staff-btn text-sm text-blue-600 hover:text-blue-800 mt-3 flex items-center font-medium"><Plus className="w-4 h-4 mr-1" /> Add Role</button>
                                </div>
                                <div>
                                    <h6 className="font-semibold text-slate-600 mb-2">Front of House</h6>
                                    {fohStaff.map(staff => renderStaffInput(staff, dayIndex, serviceIndex))}
                                    <button type="button" onClick={() => handleAddStaff(dayIndex, serviceIndex, 'Front of House')} className="add-staff-btn text-sm text-blue-600 hover:text-blue-800 mt-3 flex items-center font-medium"><Plus className="w-4 h-4 mr-1" /> Add Role</button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Operations & Daily Revenue</h2>
                <p className="text-slate-500">Plan your staffing and operations for each service with precision.</p>
            </div>
            <div className="space-y-4 max-h-[60vh] overflow-y-auto pr-4">
                {schedule.map((day, dayIndex) => {
                    const isCollapsed = !!collapsedDays[day.dayName];
                    return (
                        <div key={day.dayName} className="day-card bg-white border border-slate-200 rounded-xl shadow-sm overflow-hidden">
                            <div onClick={() => toggleDayCollapse(day.dayName)} className="day-header flex items-center justify-between p-4 cursor-pointer">
                                <div className="flex items-center gap-4">
                                    <ChevronDown className={`day-toggle-icon w-5 h-5 text-slate-500 transition-transform duration-300 ${isCollapsed ? '-rotate-90' : ''}`} />
                                    <h3 className="text-xl font-bold text-slate-800">{day.dayName}</h3>
                                </div>
                                <div className="flex items-center gap-3" onClick={(e) => e.stopPropagation()}>
                                    <span className={`text-sm font-medium ${day.isClosed ? 'text-red-500' : 'text-slate-600'}`}>{day.isClosed ? 'Closed' : 'Open'}</span>
                                    <label className="flex items-center cursor-pointer">
                                        <input type="checkbox" checked={day.isClosed} onChange={(e) => handleDayValueChange(dayIndex, 'isClosed', e.target.checked)} className="day-closed-toggle sr-only peer" />
                                        <div className="relative w-11 h-6 bg-slate-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-500"></div>
                                    </label>
                                </div>
                            </div>
                            {!isCollapsed && (
                                <div className={`day-content transition-all duration-500 ease-in-out ${day.isClosed ? 'bg-slate-50' : ''}`}>
                                    <div className="p-4 border-t border-slate-200">
                                        {!day.isClosed && (
                                            <>
                                                <div className="flex items-center space-x-4 mb-6">
                                                    <label className="text-sm font-medium text-slate-700 whitespace-nowrap">Restaurant Hours:</label>
                                                    <input type="time" value={day.openingTime} onChange={(e) => handleDayValueChange(dayIndex, 'openingTime', e.target.value)} className="day-time-input w-full p-2 border border-slate-300 rounded-md" />
                                                    <span className="text-slate-500">-</span>
                                                    <input type="time" value={day.closingTime} onChange={(e) => handleDayValueChange(dayIndex, 'closingTime', e.target.value)} className="day-time-input w-full p-2 border border-slate-300 rounded-md" />
                                                </div>
                                                <div className="space-y-4">
                                                    {day.services.map((service, serviceIndex) => renderService(service, dayIndex, serviceIndex, day.isClosed))}
                                                </div>
                                            </>
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </>
    );
};

const Step4Tables: React.FC<{
    tableLayout: 'section' | 'flow';
    setTableLayout: React.Dispatch<React.SetStateAction<'section' | 'flow'>>;
    tableSections: TableSection[];
    setTableSections: React.Dispatch<React.SetStateAction<TableSection[]>>;
    flowTables: Table[];
    setFlowTables: React.Dispatch<React.SetStateAction<Table[]>>;
    setShowAddSectionModal: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ tableLayout, setTableLayout, tableSections, setTableSections, flowTables, setFlowTables, setShowAddSectionModal }) => {
    
    const [nextFlowTableNumber, setNextFlowTableNumber] = useState(1);

    const handleSectionTableCountChange = (sectionId: number, targetCount: number) => {
        setTableSections(sections => sections.map(sec => {
            if (sec.id === sectionId) {
                const currentCount = sec.tables.length;
                let newTables = [...sec.tables];
                const prefix = sec.name.charAt(0).toUpperCase();

                if (targetCount > currentCount) {
                    for (let i = 0; i < targetCount - currentCount; i++) {
                        const newNumber = currentCount + i + 1;
                        newTables.push({ 
                            id: Date.now() + i,
                            name: `${prefix}${newNumber}`,
                            capacity: 4,
                            type: 'Indoor'
                        });
                    }
                } else if (targetCount < currentCount) {
                    newTables = newTables.slice(0, targetCount);
                }
                return { ...sec, tables: newTables };
            }
            return sec;
        }));
    };
    
    const handleFlowTableCountChange = (targetCount: number) => {
        const currentCount = flowTables.length;
        let newTables = [...flowTables];

        if (targetCount > currentCount) {
            for (let i = 0; i < targetCount - currentCount; i++) {
                const newNumber = nextFlowTableNumber + i;
                newTables.push({
                    id: Date.now() + i,
                    name: `T${newNumber}`,
                    capacity: 4,
                    type: 'Indoor'
                });
            }
            setNextFlowTableNumber(prev => prev + (targetCount - currentCount));
        } else if (targetCount < currentCount) {
            newTables = newTables.slice(0, targetCount);
        }
        setFlowTables(newTables);
    };

    const handleDeleteTable = (tableId: number, sectionId?: number) => {
        if (tableLayout === 'section' && sectionId) {
            setTableSections(sections => sections.map(sec => 
                sec.id === sectionId ? { ...sec, tables: sec.tables.filter(t => t.id !== tableId) } : sec
            ));
        } else {
            setFlowTables(flowTables.filter(t => t.id !== tableId));
        }
    };

    const handleTableChange = (tableId: number, field: keyof Omit<Table, 'id'>, value: any, sectionId?: number) => {
        const updateLogic = (tables: Table[]) => tables.map(t => 
            t.id === tableId ? { ...t, [field]: value } : t
        );

        if (tableLayout === 'section' && sectionId) {
            setTableSections(sections => sections.map(sec => 
                sec.id === sectionId ? { ...sec, tables: updateLogic(sec.tables) } : sec
            ));
        } else {
            setFlowTables(updateLogic(flowTables));
        }
    };
    
    const handleDeleteSection = (sectionId: number) => {
        setTableSections(sections => sections.filter(sec => sec.id !== sectionId));
    };

    const renderTableHeader = () => (
        <div className="grid grid-cols-12 items-center gap-3 px-2 mb-1">
            <label className="col-span-8 text-xs font-semibold text-slate-500 uppercase">Table Name</label>
            <label className="col-span-3 text-xs font-semibold text-slate-500 uppercase">Seats</label>
        </div>
    );
    
    const renderTableInputRow = (table: Table, sectionId?: number) => (
        <div key={table.id} className="table-row grid grid-cols-12 items-center gap-3 px-2">
            <input type="text" value={table.name} onChange={(e) => handleTableChange(table.id, 'name', e.target.value, sectionId)} className="col-span-8 form-input text-sm p-1.5 border border-slate-300 rounded-md shadow-sm" />
            <input type="number" value={table.capacity} min="1" onChange={(e) => handleTableChange(table.id, 'capacity', parseInt(e.target.value) || 1, sectionId)} className="col-span-3 form-input text-sm p-1.5 border border-slate-300 rounded-md shadow-sm" />
            <button type="button" onClick={() => handleDeleteTable(table.id, sectionId)} className="remove-table-btn text-slate-400 hover:text-red-500 col-span-1 transition-colors"><X className="w-4 h-4 mx-auto" /></button>
        </div>
    );
    
    const renderFloorPlanPreview = () => {
        const renderTableElement = (table: Table) => (
            <div key={table.id} className="border-2 rounded-lg p-2 text-center text-xs font-medium bg-blue-100 border-blue-300 text-blue-800 shadow-sm">
                <div className="font-bold text-sm">{table.name}</div>
                <div className="text-slate-600">{table.capacity} seats</div>
            </div>
        );

        if (tableLayout === 'section') {
            if (tableSections.length === 0) return <p className="text-center text-slate-400 p-4">Add a section to begin.</p>;
            return (
                <div className="space-y-4">
                    {tableSections.map(sec => (
                        <div key={sec.id} className="floor-plan-section border-2 border-dashed border-slate-300 rounded-xl p-4 bg-white/30">
                            <h5 className="floor-plan-section-title font-semibold text-slate-700 mb-3">{sec.name}</h5>
                            <div className="floor-plan-table-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                                {sec.tables.length > 0 ? sec.tables.map(renderTableElement) : <p className="text-slate-400 text-xs col-span-full">No tables in this section.</p>}
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        if (flowTables.length === 0) return <p className="text-center text-slate-400 p-4">Add tables to see a preview.</p>;
        return (
            <div className="floor-plan-table-grid grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                {flowTables.map(renderTableElement)}
            </div>
        );
    };

    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">Table Configuration</h2>
                <p className="text-slate-500">Design your restaurant's floor plan.</p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-6">
                    <div>
                        <label className="block text-sm font-semibold text-slate-700 mb-2">Layout Style</label>
                        <div className="grid grid-cols-2 gap-4">
                            {(['section', 'flow'] as ('section' | 'flow')[]).map(style => (
                                <div key={style} className="selectable-option">
                                    <input type="radio" id={`layout-${style}`} name="layoutStyle" value={style} checked={tableLayout === style} onChange={() => setTableLayout(style)} className="hidden" />
                                    <label htmlFor={`layout-${style}`} className={`block text-center p-4 border-2 rounded-xl cursor-pointer font-medium transition-all duration-200 ${tableLayout === style ? 'border-blue-500 bg-blue-50 text-blue-700 shadow-md' : 'border-slate-200 bg-white hover:border-blue-300 hover:shadow-sm'}`}>
                                        {style === 'section' ? <LayoutGridIcon className="mx-auto mb-2 w-6 h-6" /> : <Move3dIcon className="mx-auto mb-2 w-6 h-6" />}
                                        {style === 'section' ? 'Section-based' : 'Free Flow'}
                                    </label>
                                </div>
                            ))}
                        </div>
                    </div>

                    {tableLayout === 'section' ? (
                        <div className="space-y-4">
                            <button type="button" onClick={() => setShowAddSectionModal(true)} className="w-full py-3 text-sm font-semibold rounded-lg flex items-center justify-center gap-2 bg-white border border-slate-300 hover:bg-slate-100 transition-colors shadow-sm">
                                <Plus className="w-4 h-4" /> Add New Section
                            </button>
                            <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
                                {tableSections.map(sec => (
                                    <div key={sec.id} className="table-section p-4 bg-white border border-slate-200 rounded-lg shadow-sm">
                                        <div className="flex justify-between items-center mb-3 pb-2 border-b">
                                            <h4 className="flex-grow text-center font-semibold text-slate-800">{sec.name}</h4>
                                            <div className="flex items-center gap-2 flex-shrink-0">
                                                <label className="text-sm font-medium text-slate-600">Tables:</label>
                                                <input type="number" value={sec.tables.length} min="0" onChange={(e) => handleSectionTableCountChange(sec.id, parseInt(e.target.value) || 0)} className="form-input w-20 text-sm p-1.5 border border-slate-300 rounded-md shadow-sm" />
                                            </div>
                                        </div>
                                        <div className="space-y-2 mb-3">
                                            {sec.tables.length > 0 && renderTableHeader()}
                                            {sec.tables.map(table => renderTableInputRow(table, sec.id))}
                                        </div>
                                        <button onClick={() => handleDeleteSection(sec.id)} className="w-full text-xs text-red-600 hover:text-red-800 font-medium flex items-center justify-center gap-1 py-1 border border-dashed border-red-200 rounded-md hover:bg-red-50 transition-colors">
                                            Delete Section
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            <div className="flex justify-between items-center">
                                <h3 className="text-lg font-semibold text-slate-700">Tables</h3>
                                <div className="flex items-center gap-2">
                                    <label className="text-sm font-medium text-slate-600">Tables:</label>
                                    <input type="number" value={flowTables.length} min="0" onChange={(e) => handleFlowTableCountChange(parseInt(e.target.value) || 0)} className="form-input w-20 text-sm p-1.5 border border-slate-300 rounded-md shadow-sm" />
                                </div>
                            </div>
                            <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                                {flowTables.length > 0 && renderTableHeader()}
                                {flowTables.map(table => renderTableInputRow(table))}
                            </div>
                        </div>
                    )}
                </div>
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-slate-700">Floor Plan Preview</h3>
                    <div className="bg-gradient-to-br from-slate-100 to-slate-200 border border-slate-300 rounded-xl p-4 min-h-[400px]">
                        {renderFloorPlanPreview()}
                    </div>
                </div>
            </div>
        </>
    );
};

const Step5Summary: React.FC<{ allData: any }> = ({ allData }) => {
    const { formData, schedule, tableLayout, tableSections, flowTables } = allData;
    const allTables = tableLayout === 'section' ? tableSections.flatMap((s: TableSection) => s.tables) : flowTables;
    const totalCapacity = allTables.reduce((sum: number, t: Table) => sum + Number(t.capacity || 0), 0);

    return (
        <>
            <div className="mb-8">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Review & Confirm</h2>
                <p className="text-slate-500">Please review all your information before proceeding.</p>
            </div>
             <div className="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm max-h-[60vh] overflow-y-auto">
                <div className="bg-white p-6 rounded-xl border border-slate-200">
                    <h4 className="font-bold text-slate-800 flex items-center gap-2 mb-4"><UserCheck className="w-5 h-5 text-blue-600" /> Account & Business</h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div><p className="text-xs text-slate-500 uppercase">Email</p><p className="font-semibold text-slate-700">{formData.email || 'N/A'}</p></div>
                        <div><p className="text-xs text-slate-500 uppercase">Business Name</p><p className="font-semibold text-slate-700">{formData.businessName || 'N/A'}</p></div>
                        <div><p className="text-xs text-slate-500 uppercase">Business Type</p><p className="font-semibold text-slate-700">{formData.businessType || 'N/A'}</p></div>
                        <div><p className="text-xs text-slate-500 uppercase">Cuisines</p><p className="font-semibold text-slate-700">{formData.cuisines.join(', ') || 'N/A'}</p></div>
                    </div>
                </div>
                 <div className="bg-white p-6 rounded-xl border border-slate-200">
                    <h4 className="font-bold text-slate-800 flex items-center gap-2 mb-4"><Clock className="w-5 h-5 text-green-600" /> Operations</h4>
                    {schedule.map((day: DaySchedule) => (
                        <div key={day.dayName} className="mt-2 text-xs">
                            <p className="font-bold text-slate-700">{day.dayName}: {day.isClosed ? 'Closed' : `${day.openingTime} - ${day.closingTime}`}</p>
                        </div>
                    ))}
                </div>
                 <div className="bg-white p-6 rounded-xl border border-slate-200">
                    <h4 className="font-bold text-slate-800 flex items-center gap-2 mb-4"><LayoutIcon className="w-5 h-5 text-purple-600" /> Table Configuration</h4>
                     <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                        <div className="text-center p-3 bg-slate-50 rounded-lg"><p className="text-2xl font-bold text-slate-800">{allTables.length}</p><p className="text-xs text-slate-500">Total Tables</p></div>
                        <div className="text-center p-3 bg-slate-50 rounded-lg"><p className="text-2xl font-bold text-slate-800">{totalCapacity}</p><p className="text-xs text-slate-500">Total Seats</p></div>
                        <div className="text-center p-3 bg-slate-50 rounded-lg"><p className="text-2xl font-bold text-slate-800">{allTables.length > 0 ? (totalCapacity / allTables.length).toFixed(1) : 0}</p><p className="text-xs text-slate-500">Avg. per Table</p></div>
                    </div>
                </div>
            </div>
        </>
    );
};

const Step6Personal: React.FC<{ formData: any, handleChange: any }> = ({ formData, handleChange }) => (
    <>
        <div className="mb-8">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Your Personal Details</h2>
            <p className="text-slate-500">We need this to verify your account.</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormInput icon={User} label="First Name" name="firstName" type="text" placeholder="Your first name" value={formData.firstName} onChange={handleChange} required />
            <FormInput icon={User} label="Last Name" name="lastName" type="text" placeholder="Your last name" value={formData.lastName} onChange={handleChange} required />
            <FormInput icon={Phone} label="Phone Number" name="phone" type="tel" placeholder="e.g. ***********" value={formData.phone} onChange={handleChange} required className="md:col-span-2" />
            <FormInput icon={MapPin} label="Address" name="address" type="text" placeholder="Street address" value={formData.address} onChange={handleChange} required className="md:col-span-2" />
            <FormInput icon={Building} label="City" name="city" type="text" placeholder="Your city" value={formData.city} onChange={handleChange} required />
            <FormInput icon={MailOpen} label="Postcode" name="postcode" type="text" placeholder="e.g. SW1A 0AA" value={formData.postcode} onChange={handleChange} required />
        </div>
        <div className="mt-8">
            <div className="flex items-center">
                <input type="checkbox" id="terms-checkbox" name="terms" checked={formData.terms} onChange={handleChange} className="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500" />
                <label htmlFor="terms-checkbox" className="ml-3 text-sm text-slate-600">
                    I agree to the <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a> and <a href="#" className="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a>
                </label>
            </div>
        </div>
    </>
);


// --- MAIN APP COMPONENT --- //

export const SignUp: React.FC = () => {
    const [currentStep, setCurrentStep] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [showSuccessModal, setShowSuccessModal] = useState(false);
    const [showAddSectionModal, setShowAddSectionModal] = useState(false);
    const [newSectionName, setNewSectionName] = useState("");
    
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        businessName: '',
        businessType: '',
        cuisines: [] as string[],
        firstName: '',
        lastName: '',
        phone: '',
        address: '',
        city: '',
        postcode: '',
        terms: false,
    });
    
    const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '' });
    const [schedule, setSchedule] = useState<DaySchedule[]>(() => DAYS_OF_WEEK.map(createInitialDay));
    const [tableLayout, setTableLayout] = useState<'section' | 'flow'>('section');
    const [tableSections, setTableSections] = useState<TableSection[]>([{ id: Date.now(), name: 'Main Dining', tables: [] }]);
    const [flowTables, setFlowTables] = useState<Table[]>([]);
    
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const { name, value, type } = e.target;
        
        if (type === 'checkbox') {
            const { checked } = e.target as HTMLInputElement;
            if (name === 'cuisine') {
                setFormData(prev => ({
                    ...prev,
                    cuisines: checked ? [...prev.cuisines, value] : prev.cuisines.filter(c => c !== value)
                }));
            } else {
                 setFormData(prev => ({ ...prev, [name]: checked }));
            }
        } else {
            setFormData(prev => ({ ...prev, [name]: value }));
        }
    };

    const handleNext = () => {
        if (currentStep < STEP_CONFIG.length - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrev = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };
    
    const handleAddSection = () => {
        if (newSectionName.trim()) {
            setTableSections(prev => [...prev, { id: Date.now(), name: newSectionName.trim(), tables: [] }]);
            setNewSectionName("");
            setShowAddSectionModal(false);
        }
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (!formData.terms) {
            alert("Please agree to the terms of service.");
            return;
        }
        setIsSubmitting(true);
        console.log("Form Data Submitted: ", { formData, schedule, tableLayout, tableSections, flowTables });
        setTimeout(() => {
            setIsSubmitting(false);
            setShowSuccessModal(true);
        }, 1500);
    };

    const renderStepContent = () => {
        switch (currentStep) {
            case 0:
                return <Step1Account formData={formData} handleChange={handleChange} passwordStrength={passwordStrength} setPasswordStrength={setPasswordStrength} />;
            case 1:
                return <Step2Business formData={formData} handleChange={handleChange} />;
            case 2:
                 return <Step3Operations schedule={schedule} setSchedule={setSchedule} />;
            case 3:
                 return <Step4Tables 
                            tableLayout={tableLayout} setTableLayout={setTableLayout} 
                            tableSections={tableSections} setTableSections={setTableSections}
                            flowTables={flowTables} setFlowTables={setFlowTables}
                            setShowAddSectionModal={setShowAddSectionModal}
                        />;
            case 4:
                 return <Step5Summary allData={{ formData, schedule, tableLayout, tableSections, flowTables }} />;
            case 5:
                 return <Step6Personal formData={formData} handleChange={handleChange} />;
            default:
                return <div>Unknown Step</div>;
        }
    };

    return (
        <>
            <div className="min-h-screen flex items-center justify-center p-4 bg-slate-100 font-sans">
                <div className="w-full max-w-5xl flex flex-col main-container rounded-2xl overflow-hidden my-8">
                    <div className="w-full top-panel p-8">
                        <div className="flex justify-between items-center mb-6">
                            <a href="#" className="flex items-center gap-3 group">
                                <div className="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                                    <ChefHat className="w-6 h-6 text-white" />
                                </div>
                                <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
                            </a>
                            <div className="text-sm text-slate-500" id="progress-text">
                                Step {currentStep + 1} of {STEP_CONFIG.length} - {STEP_CONFIG[currentStep].label}
                            </div>
                        </div>
                        <StepIndicator currentStep={currentStep} />
                    </div>

                    <div className="w-full p-8 sm:p-14">
                        <form onSubmit={handleSubmit}>
                            <div className="step-section active">
                                {renderStepContent()}
                            </div>
                            <ActionButtons
                                onNext={handleNext}
                                onPrev={currentStep > 0 ? handlePrev : undefined}
                                isSubmitting={isSubmitting}
                                isFinalStep={currentStep === STEP_CONFIG.length - 1}
                                nextText={currentStep === STEP_CONFIG.length - 2 ? "Review & Confirm" : "Next Step"}
                            />
                        </form>
                    </div>
                </div>
            </div>

            {/* Modals */}
            {showAddSectionModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white p-8 rounded-2xl shadow-2xl max-w-sm w-full mx-4">
                        <h3 className="text-xl font-bold text-slate-800 mb-4">Add New Section</h3>
                        <FormInput name="newSectionName" type="text" placeholder="e.g. Patio, Bar Area" value={newSectionName} onChange={e => setNewSectionName(e.target.value)} />
                        <div className="mt-6 flex justify-end gap-4">
                            <button type="button" onClick={() => setShowAddSectionModal(false)} className="btn-secondary px-4 py-2 text-sm font-semibold rounded-lg">Cancel</button>
                            <button type="button" onClick={handleAddSection} className="btn-primary px-4 py-2 text-sm font-semibold rounded-lg">Add Section</button>
                        </div>
                    </div>
                </div>
            )}

            {showSuccessModal && (
                <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
                    <div className="bg-white p-8 rounded-2xl shadow-2xl max-w-md w-full mx-4 text-center animate-fadeInUp">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Check className="w-8 h-8 text-green-600" />
                        </div>
                        <h3 className="text-2xl font-bold text-slate-800 mb-2">Account Created!</h3>
                        <p className="text-slate-600 mb-6">Welcome to RestroManager, {formData.firstName || 'friend'}!</p>
                        <button onClick={() => alert("Redirecting to dashboard...")} className="btn-primary w-full py-3 text-white rounded-xl font-semibold">
                            Go to Dashboard
                        </button>
                    </div>
                </div>
            )}
            
            <style>{`
                :root { --primary-600: #2563eb; }
                body { font-family: 'Inter', sans-serif; background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%); }
                .main-container { background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
                .top-panel { background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); border-bottom: 1px solid #e2e8f0; }
                .step-section.active { animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
                @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px) scale(0.95); } to { opacity: 1; transform: translateY(0) scale(1); } }
                .step-indicator { transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); }
                .step-indicator .step-circle { transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
                .step-indicator.active .step-circle { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; border-color: #2563eb; box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4); transform: scale(1.1); }
                .step-indicator.active .step-label { color: #1e293b; font-weight: 600; }
                .step-indicator.completed .step-circle { background: linear-gradient(135deg, #10b981, #059669); color: white; border-color: #10b981; box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3); }
                .step-connector { transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); background-color: #e2e8f0; }
                .step-connector.completed { background: linear-gradient(to right, #10b981, #059669); }
                .step-connector-wrapper .step-connector.active { background: linear-gradient(to right, #10b981, #e2e8f0); }
                .step-indicator.completed + .step-connector-wrapper .step-connector { background: #10b981; }
                .step-indicator.active + .step-connector-wrapper .step-connector { background: linear-gradient(to right, #10b981, #e2e8f0); }
                .form-input.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
                .form-input.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
                .form-input.p-1 { padding: 0.25rem; }
                .form-input.p-1_5 { padding: 0.375rem; }
                .form-input.p-2 { padding: 0.5rem; }
                .form-input.p-2_5 { padding: 0.625rem; }
                .form-input:focus { transform: translateY(-1px); box-shadow: 0 4px 15px rgba(37, 99, 235, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1); }
                .btn-primary { background: linear-gradient(135deg, #3b82f6, #2563eb); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4); }
                .btn-primary:hover:not(:disabled) { background: linear-gradient(135deg, #2563eb, #1d4ed8); transform: translateY(-2px); box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5); }
                .btn-secondary { background: linear-gradient(135deg, #f1f5f9, #e2e8f0); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); }
                .btn-secondary:hover { background: linear-gradient(135deg, #e2e8f0, #cbd5e1); transform: translateY(-1px); }
                .loading-spinner { display: inline-block; width: 16px; height: 16px; border: 2px solid #ffffff; border-radius: 50%; border-top-color: transparent; animation: spin 1s ease-in-out infinite; }
                @keyframes spin { to { transform: rotate(360deg); } }
                .selectable-option input:checked + label { border-color: #2563eb; background: linear-gradient(135deg, #eff6ff, #dbeafe); color: #1d4ed8; box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2); transform: translateY(-2px); }
                .selectable-option label:hover { border-color: #93c5fd; transform: translateY(-1px); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
                .grid-cols-auto-fill-80 { grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); }
            `}</style>
        </>
    );
};

export default SignUp;
