import React, { type InputHTMLAttributes, forwardRef } from 'react';
import { type LucideIcon } from 'lucide-react';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: string;
  icon?: LucideIcon;
  rightIcon?: React.ReactNode;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  success,
  icon: Icon,
  rightIcon,
  className = '',
  ...props
}, ref) => {
  const hasError = !!error;
  const hasSuccess = !!success && !hasError;
  
  return (
    <div className="form-group">
      {label && (
        <label className="block text-sm font-semibold text-slate-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        {Icon && (
          <Icon className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors" />
        )}
        
        <input
          ref={ref}
          className={`
            form-input block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm 
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
            ${Icon ? 'pl-12' : ''}
            ${rightIcon ? 'pr-12' : ''}
            ${hasError ? 'error border-red-500' : ''}
            ${hasSuccess ? 'success border-green-500' : ''}
            ${className}
          `}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute right-4 top-1/2 -translate-y-1/2">
            {rightIcon}
          </div>
        )}
      </div>
      
      <div className="validation-message">
        {error && (
          <div className="error-message">
            <span className="text-xs">⚠</span>
            {error}
          </div>
        )}
        {success && !error && (
          <div className="success-message">
            <span className="text-xs">✓</span>
            {success}
          </div>
        )}
      </div>
    </div>
  );
});
