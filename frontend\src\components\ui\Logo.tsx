import React from 'react';
import { ChefHat } from 'lucide-react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

export const Logo: React.FC<LogoProps> = ({ size = 'md', showText = true }) => {
  const sizeClasses = {
    sm: { icon: 'w-6 h-6', text: 'text-lg', padding: 'p-2' },
    md: { icon: 'w-8 h-8', text: 'text-2xl', padding: 'p-3' },
    lg: { icon: 'w-12 h-12', text: 'text-3xl', padding: 'p-4' }
  };

  const classes = sizeClasses[size];

  return (
    <div className="flex items-center justify-center gap-3">
      <div className={`${classes.padding} rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg`}>
        <ChefHat className={`${classes.icon} text-white`} />
      </div>
      {showText && (
        <h1 className={`${classes.text} font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent`}>
          RestroManager
        </h1>
      )}
    </div>
  );
};
