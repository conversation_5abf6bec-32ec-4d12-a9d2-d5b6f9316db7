// PWA Utility Functions for RestroManager - React/TypeScript version

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
  }
  interface Navigator {
    standalone?: boolean;
  }
}

class PWAManager {
  private deferredPrompt: BeforeInstallPromptEvent | null = null;

  constructor() {
    this.init();
  }

  private init() {
    this.setupInstallPrompt();
    this.checkInstallStatus();
  }

  private setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e: Event) => {
      console.log('PWA install prompt available');
      e.preventDefault();
      this.deferredPrompt = e as BeforeInstallPromptEvent;
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('P<PERSON> was installed successfully');
      this.hideInstallButton();
      this.deferredPrompt = null;
    });
  }

  private checkInstallStatus() {
    // Check if app is already installed
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('PWA is running in standalone mode');
      return;
    }

    // Check if running as PWA
    if (window.navigator.standalone === true) {
      console.log('PWA is running in standalone mode (iOS)');
      return;
    }
  }

  public async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }

    try {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        this.deferredPrompt = null;
        return true;
      } else {
        console.log('User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('Error during install prompt:', error);
      return false;
    }
  }

  public showInstallButton() {
    // This would be called to show install button in UI
    console.log('Install button should be shown');
  }

  private hideInstallButton() {
    // This would be called to hide install button in UI
    console.log('Install button should be hidden');
  }

  public isInstallable(): boolean {
    return this.deferredPrompt !== null;
  }

  public isInstalled(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
  }
}

// Export singleton instance
export const pwaManager = new PWAManager();

// Utility functions
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered successfully');
      return registration;
    } catch (error) {
      console.error('Service Worker registration failed:', error);
      return null;
    }
  }
  return null;
};

export const checkOnlineStatus = (): boolean => {
  return navigator.onLine;
};

export const addOfflineListener = (callback: (isOnline: boolean) => void): void => {
  window.addEventListener('online', () => callback(true));
  window.addEventListener('offline', () => callback(false));
};

// Initialize PWA functionality
export const initializePWA = (): PWAManager => {
  console.log('🚀 Initializing PWA Manager...');

  // Register service worker
  registerServiceWorker();

  // Show install button after a short delay
  setTimeout(() => {
    pwaManager.showInstallButton();
  }, 1000);

  return pwaManager;
};

// Get PWA manager instance
export const getPWAManager = (): PWAManager => pwaManager;
