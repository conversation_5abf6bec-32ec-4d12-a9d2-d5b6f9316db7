<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - RestroManager</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .container {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 2rem;
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }
        
        h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        p {
            color: #64748b;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 2rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .feature {
            background: rgba(59, 130, 246, 0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: #475569;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📶</div>
        <h1>You're Offline</h1>
        <p>It looks like you've lost your internet connection. Don't worry, RestroManager works offline too!</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">
                <strong>Cached Data</strong><br>
                View previously loaded content
            </div>
            <div class="feature">
                <strong>Offline Mode</strong><br>
                Continue working seamlessly
            </div>
        </div>
    </div>
    
    <script>
        // Check for connection and auto-reload when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show connection status
        if (navigator.onLine) {
            window.location.reload();
        }
    </script>
</body>
</html>
