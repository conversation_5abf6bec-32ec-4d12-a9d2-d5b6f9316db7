import React from 'react';

interface RoleCardProps {
  role: string;
  emoji: string;
  title: string;
  description: string;
  isSelected: boolean;
  onClick: () => void;
}

export const RoleCard: React.FC<RoleCardProps> = ({
  role,
  emoji,
  title,
  description,
  isSelected,
  onClick
}) => {
  return (
    <div
      className={`
        role-card border-2 rounded-xl p-4 text-center cursor-pointer
        transition-all duration-300 hover:transform hover:-translate-y-1 hover:shadow-lg
        ${isSelected 
          ? 'border-blue-600 bg-gradient-to-br from-blue-50 to-blue-100 transform -translate-y-1 shadow-lg' 
          : 'border-slate-200 hover:border-slate-300'
        }
      `}
      onClick={onClick}
      data-role={role}
    >
      <div className="text-2xl mb-2">{emoji}</div>
      <div className="text-sm font-semibold text-slate-700">{title}</div>
      <div className="text-xs text-slate-500">{description}</div>
    </div>
  );
};
