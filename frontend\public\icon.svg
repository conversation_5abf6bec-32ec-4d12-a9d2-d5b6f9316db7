<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="512" height="512" rx="64" fill="url(#gradient)"/>
  
  <!-- Chef Hat Icon -->
  <g transform="translate(128, 128)">
    <!-- Hat Base -->
    <path d="M64 192C64 192 32 192 32 224C32 256 64 256 64 256H192C192 256 224 256 224 224C224 192 192 192 192 192" fill="white" opacity="0.9"/>
    
    <!-- Hat Top -->
    <path d="M128 64C104 64 84 84 84 108C84 132 104 152 128 152C152 152 172 132 172 108C172 84 152 64 128 64Z" fill="white" opacity="0.9"/>
    
    <!-- Hat Puffs -->
    <circle cx="96" cy="96" r="20" fill="white" opacity="0.8"/>
    <circle cx="160" cy="96" r="20" fill="white" opacity="0.8"/>
    <circle cx="128" cy="80" r="16" fill="white" opacity="0.8"/>
    
    <!-- Hat Band -->
    <rect x="64" y="180" width="128" height="24" fill="white" opacity="0.7"/>
  </g>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
