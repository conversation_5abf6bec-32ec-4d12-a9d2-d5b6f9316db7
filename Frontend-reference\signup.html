<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - RestroManager</title>

    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="/browserconfig.xml">

    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- PWA Icons - with fallbacks -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 32 32%22><rect width=%2232%22 height=%2232%22 fill=%22%232563eb%22/><text x=%2216%22 y=%2220%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2212%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 16 16%22><rect width=%2216%22 height=%2216%22 fill=%22%232563eb%22/><text x=%228%22 y=%2212%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%228%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png" onerror="this.href='data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 180 180%22><rect width=%22180%22 height=%22180%22 fill=%22%232563eb%22/><text x=%2290%22 y=%22110%22 text-anchor=%22middle%22 fill=%22white%22 font-family=%22Arial%22 font-size=%2260%22 font-weight=%22bold%22>RM</text></svg>'">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Tailwind CSS configuration
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>

    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>

    <style>
        /* Fallback styles in case Tailwind doesn't load */
        .fallback-styles {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f1f5f9;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fallback-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 1200px;
            width: 100%;
            overflow: hidden;
        }

        .fallback-header {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 32px;
            border-bottom: 1px solid #e2e8f0;
        }

        .fallback-content {
            padding: 32px;
        }

        .fallback-button {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 16px 24px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 16px;
        }

        .fallback-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            margin-bottom: 16px;
            font-size: 16px;
        }

        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --success-500: #10b981;
            --error-500: #ef4444;
            --warning-500: #f59e0b;
        }

        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }

        .main-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .top-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            position: relative;
            border-bottom: 1px solid #e2e8f0;
        }

        .step-section {
            display: none;
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step-section.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .step-indicator {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .step-indicator .step-circle {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .step-indicator.active .step-circle {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border-color: #2563eb;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
            transform: scale(1.1);
        }
        
        .step-indicator.active .step-label {
             color: #1e293b;
             font-weight: 600;
        }

        .step-indicator.completed .step-circle {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        .step-indicator.completed .step-label {
             color: #1e293b;
             font-weight: 600;
        }
        
        .step-connector {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background-color: #e2e8f0;
        }

        .step-indicator.completed .step-connector {
            background: linear-gradient(to right, #10b981, #059669);
        }
        
        .step-indicator.active .step-connector {
             background: linear-gradient(to right, #10b981, #e2e8f0);
        }


        .progress-bar {
            position: absolute;
            top: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6);
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        .form-input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
        }

        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .success-message {
            color: #10b981;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
        }

        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-1px);
        }

        .selectable-option {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .selectable-option input:checked + label {
            border-color: #2563eb;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            color: #1d4ed8;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
            transform: translateY(-2px);
        }

        .selectable-option label:hover {
            border-color: #93c5fd;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-row {
            animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transition: all 0.3s ease;
        }

        .table-row:hover {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            transform: translateX(4px);
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .cuisine-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        .drag-drop-area {
            border: 2px dashed #cbd5e1;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }

        .drag-drop-area.dragover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
        }
        
        #floor-plan-sections {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .floor-plan-section {
            border: 2px dashed #cbd5e1;
            border-radius: 0.75rem;
            padding: 1rem;
        }

        .floor-plan-section-title {
            font-weight: 600;
            color: #475569;
            margin-bottom: 0.75rem;
        }

        .floor-plan-table-grid {
             display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 1rem;
        }
        
        .price-slider-thumb::-webkit-slider-thumb {
            -webkit-appearance: none;
            margin-top: -8px; /* Offset to center the thumb on the track */
            height: 1.25rem;
            width: 1.25rem;
            background-color: white;
            border: 2px solid #2563eb;
            border-radius: 9999px;
            cursor: pointer;
            pointer-events: auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .price-slider-thumb::-moz-range-thumb {
            height: 1.25rem;
            width: 1.25rem;
            background-color: white;
            border: 2px solid #2563eb;
            border-radius: 9999px;
            cursor: pointer;
            pointer-events: auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        @media (max-width: 768px) {
            .step-section {
                padding: 1rem;
            }

            .main-container {
                margin: 0.5rem;
                border-radius: 1rem;
            }
        }
    </style>
</head>
<body class="bg-slate-100">
    <!-- Loading indicator -->
    <div id="loading-indicator" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: #f1f5f9; display: flex; align-items: center; justify-content: center; z-index: 9999;">
        <div style="text-align: center;">
            <div style="width: 40px; height: 40px; border: 4px solid #e2e8f0; border-top: 4px solid #2563eb; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
            <p style="color: #64748b; font-family: Inter, sans-serif;">Loading RestroManager...</p>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="w-full max-w-5xl flex flex-col main-container rounded-2xl overflow-hidden my-8">
            <!-- Top Panel: Progress & Branding -->
            <div class="w-full top-panel p-8">
                <div class="flex justify-between items-center mb-6">
                    <a href="#" class="flex items-center gap-3 group">
                        <div class="p-2 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg group-hover:shadow-xl transition-all duration-300">
                            <i data-lucide="chef-hat" class="w-6 h-6 text-white"></i>
                        </div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
                    </a>
                    <div class="text-sm text-slate-500" id="progress-text">Step 1 of 6 - Account Details</div>
                </div>

                <div id="step-indicators-container" class="w-full flex items-center">
                    <!-- Step indicators will be dynamically generated here -->
                </div>
            </div>

            <!-- Bottom Panel: Form -->
            <div class="w-full p-8 sm:p-14">
                <form id="signup-form">
                    <!-- Step 1: Create Account -->
                    <div id="step-1" class="step-section active">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Create your account</h2>
                            <p class="text-slate-500">Start by setting up your login details.</p>
                        </div>

                        <div class="space-y-6">
                            <button type="button" class="w-full flex items-center justify-center gap-3 py-4 border border-slate-300 rounded-xl hover:bg-slate-50 transition-all duration-300 hover:shadow-md hover:border-slate-400 group">
                                <img src="https://www.google.com/favicon.ico" alt="Google icon" class="w-5 h-5 group-hover:scale-110 transition-transform">
                                <span class="text-sm font-medium text-slate-700">Sign up with Google</span>
                            </button>

                            <div class="flex items-center">
                                <div class="flex-grow border-t border-slate-200"></div>
                                <span class="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                                <div class="flex-grow border-t border-slate-200"></div>
                            </div>

                            <div class="space-y-4">
                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="mail" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="email" name="email" required placeholder="Email address"
                                               class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    </div>
                                    <div class="validation-message"></div>
                                </div>

                                <div class="form-group">
                                    <div class="relative">
                                        <i data-lucide="lock" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                                        <input type="password" name="password" required placeholder="Password (min. 8 characters)"
                                               class="form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <button type="button" class="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                                            <i data-lucide="eye" class="w-5 h-5"></i>
                                        </button>
                                    </div>
                                    <div class="validation-message"></div>
                                    <div class="password-strength mt-2 hidden">
                                        <div class="flex gap-1 mb-1">
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                            <div class="strength-bar h-1 bg-slate-200 rounded flex-1"></div>
                                        </div>
                                        <p class="strength-text text-xs text-slate-500"></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button type="button" data-next="1" class="next-btn btn-primary w-full px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Continue</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Business & Cuisine -->
                    <div id="step-2" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Business & Cuisine</h2>
                            <p class="text-slate-500">Tell us about your business and what you serve.</p>
                        </div>

                        <div class="space-y-6">
                            <div class="form-group">
                                <label for="business-name" class="block text-sm font-semibold text-slate-700 mb-2">Business Name</label>
                                <div class="relative">
                                    <i data-lucide="store" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="business-name" name="businessName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Enter your business name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="business-type" class="block text-sm font-semibold text-slate-700 mb-2">Business Type</label>
                                <div class="relative">
                                    <i data-lucide="utensils" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <select id="business-type" name="businessType"
                                            class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 bg-white rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select business type</option>
                                        <option value="Fine Dining">🍽️ Fine Dining</option>
                                        <option value="Casual Dining">🍕 Casual Dining</option>
                                        <option value="Cafe / Bistro">☕ Cafe / Bistro</option>
                                        <option value="Quick Service (QSR)">🍔 Quick Service (QSR)</option>
                                        <option value="Food Truck">🚚 Food Truck</option>
                                        <option value="Pub / Bar">🍺 Pub / Bar</option>
                                        <option value="Bakery">🥖 Bakery</option>
                                    </select>
                                </div>
                                <div class="validation-message"></div>
                            </div>
                            
                            <div class="form-group">
                                <label class="block text-sm font-semibold text-slate-700 mb-4">Cuisine Type (select all that apply)</label>
                                <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-british" name="cuisine" value="British" class="hidden"><label for="cuisine-british" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🇬🇧</div><div class="text-sm">British</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-indian" name="cuisine" value="Indian" class="hidden"><label for="cuisine-indian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍛</div><div class="text-sm">Indian</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-italian" name="cuisine" value="Italian" class="hidden"><label for="cuisine-italian" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍝</div><div class="text-sm">Italian</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-chinese" name="cuisine" value="Chinese" class="hidden"><label for="cuisine-chinese" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🥢</div><div class="text-sm">Chinese</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-american" name="cuisine" value="American" class="hidden"><label for="cuisine-american" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍔</div><div class="text-sm">American</div></label></div>
                                    <div class="selectable-option"><input type="checkbox" id="cuisine-other" name="cuisine" value="Other" class="hidden"><label for="cuisine-other" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium transition-all hover:border-blue-300 bg-white"><div class="cuisine-icon">🍽️</div><div class="text-sm">Other</div></label></div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="0" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="2" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 3: Operations & Daily Revenue -->
                    <div id="step-3" class="step-section">
                        <div class="mb-8">
                             <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Operations & Daily Revenue</h2>
                            <p class="text-slate-500">Plan your staffing and operations for each service with precision.</p>
                        </div>
                        <div id="operations-schedule-container" class="space-y-6 max-h-[60vh] overflow-y-auto pr-4">
                            <!-- JS generates the weekly schedule here -->
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="1" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="button" data-next="3" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 4: Table Configuration -->
                    <div id="step-4" class="step-section">
                        <div class="mb-6">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">Table Configuration</h2>
                            <p class="text-slate-500">Design your restaurant's floor plan.</p>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Left: Controls -->
                            <div class="space-y-6">
                                <div class="form-group">
                                    <label class="block text-sm font-semibold text-slate-700 mb-2">Layout Style</label>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="selectable-option">
                                            <input type="radio" id="layout-section" name="layoutStyle" value="section" class="hidden" checked>
                                            <label for="layout-section" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="layout-grid" class="mx-auto mb-2"></i>Section-based</label>
                                        </div>
                                        <div class="selectable-option">
                                            <input type="radio" id="layout-flow" name="layoutStyle" value="flow" class="hidden">
                                            <label for="layout-flow" class="block text-center p-4 border-2 border-slate-200 rounded-xl cursor-pointer font-medium"><i data-lucide="move-3d" class="mx-auto mb-2"></i>Free Flow</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Section Based UI -->
                                <div id="section-based-ui">
                                    <button type="button" id="show-add-section-modal-btn" class="w-full btn-secondary py-3 text-sm font-semibold rounded-lg flex items-center justify-center gap-2">
                                        <i data-lucide="plus" class="w-4 h-4"></i> Add New Section
                                    </button>
                                    <div id="table-sections-container" class="mt-4 space-y-4 max-h-[400px] overflow-y-auto pr-2">
                                        <!-- JS generates sections here -->
                                    </div>
                                </div>
                                
                                <!-- Flow Based UI -->
                                <div id="flow-based-ui" class="hidden">
                                     <div class="flex justify-between items-center">
                                        <h3 class="text-lg font-semibold text-slate-700">Tables</h3>
                                        <button type="button" id="add-flow-table-btn" class="btn-secondary px-4 py-2 text-sm font-semibold text-slate-700 rounded-lg flex items-center gap-2">
                                            <i data-lucide="plus" class="w-4 h-4"></i> Add Table
                                        </button>
                                    </div>
                                    <div id="flow-table-list" class="mt-4 space-y-3 max-h-[400px] overflow-y-auto pr-2">
                                        <!-- JS generates flow tables here -->
                                    </div>
                                </div>
                            </div>
                            <!-- Right: Preview -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-semibold text-slate-700">Floor Plan Preview</h3>
                                <div id="floor-plan-preview" class="bg-gradient-to-br from-slate-50 to-slate-100 border-2 border-dashed border-slate-300 rounded-xl p-4 min-h-[400px]">
                                    <!-- JS generates preview here -->
                                </div>
                            </div>
                        </div>


                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="2" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i> Back
                            </button>
                            <button type="button" data-next="4" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Next Step</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span> Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Step 5: Summary -->
                    <div id="step-5" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Review & Confirm</h2>
                            <p class="text-slate-500">Please review all your information before proceeding.</p>
                        </div>

                        <div id="summary-container" class="space-y-6 text-sm bg-gradient-to-br from-slate-50 to-slate-100 p-8 rounded-xl shadow-sm">
                            <!-- Summary content will be injected here -->
                        </div>

                        <div class="mt-8">
                            <div class="flex items-center mb-6">
                                <input type="checkbox" id="terms-checkbox" class="w-5 h-5 text-blue-600 border-slate-300 rounded focus:ring-blue-500">
                                <label for="terms-checkbox" class="ml-3 text-sm text-slate-600">
                                    I agree to the <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Privacy Policy</a>
                                </label>
                            </div>

                            <div class="flex items-center gap-4">
                                <button type="button" data-prev="3" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                    <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                    Back
                                </button>
                                <button type="button" data-next="5" class="next-btn btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span class="btn-text">Confirm & Continue</span>
                                    <span class="btn-loading hidden">
                                        <span class="loading-spinner"></span>
                                        Processing...
                                    </span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Personal Details -->
                    <div id="step-6" class="step-section">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-3">Your Personal Details</h2>
                            <p class="text-slate-500">We need this to verify your account.</p>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="first-name" class="block text-sm font-semibold text-slate-700 mb-2">First Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="first-name" name="firstName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your first name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="last-name" class="block text-sm font-semibold text-slate-700 mb-2">Last Name</label>
                                <div class="relative">
                                    <i data-lucide="user" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="last-name" name="lastName" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your last name">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="phone" class="block text-sm font-semibold text-slate-700 mb-2">Phone Number</label>
                                <div class="relative">
                                    <i data-lucide="phone" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="tel" id="phone" name="phone" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g. 07123456789">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group md:col-span-2">
                                <label for="address" class="block text-sm font-semibold text-slate-700 mb-2">Address</label>
                                <div class="relative">
                                    <i data-lucide="map-pin" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="address" name="address" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Street address">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="city" class="block text-sm font-semibold text-slate-700 mb-2">City</label>
                                <div class="relative">
                                    <i data-lucide="building" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="city" name="city" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Your city">
                                </div>
                                <div class="validation-message"></div>
                            </div>

                            <div class="form-group">
                                <label for="postcode" class="block text-sm font-semibold text-slate-700 mb-2">Postcode</label>
                                <div class="relative">
                                    <i data-lucide="mail-open" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400"></i>
                                    <input type="text" id="postcode" name="postcode" required
                                           class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="e.g. SW1A 0AA">
                                </div>
                                <div class="validation-message"></div>
                            </div>
                        </div>

                        <div class="mt-8 flex items-center gap-4">
                            <button type="button" data-prev="4" class="prev-btn btn-secondary px-6 py-4 text-sm font-semibold text-slate-700 rounded-xl focus:outline-none transition-all">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-2"></i>
                                Back
                            </button>
                            <button type="submit" class="btn-primary flex-1 px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="btn-text">Create Account</span>
                                <span class="btn-loading hidden">
                                    <span class="loading-spinner"></span>
                                    Processing...
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Add Section Modal -->
    <div id="add-section-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-sm w-full mx-4">
            <h3 class="text-xl font-bold text-slate-800 mb-4">Add New Section</h3>
            <div class="form-group">
                <label for="modal-section-name" class="block text-sm font-semibold text-slate-700 mb-2">Section Name</label>
                <input type="text" id="modal-section-name" placeholder="e.g. Patio, Bar Area" class="form-input w-full">
            </div>
            <div class="mt-6 flex justify-end gap-4">
                <button type="button" id="cancel-add-section" class="btn-secondary px-4 py-2 text-sm font-semibold rounded-lg">Cancel</button>
                <button type="button" id="confirm-add-section" class="btn-primary px-4 py-2 text-sm font-semibold rounded-lg">Add Section</button>
            </div>
        </div>
    </div>


    <script>
        // Check if Tailwind CSS loaded properly
        function checkTailwindLoaded() {
            const testElement = document.createElement('div');
            testElement.className = 'bg-blue-500';
            testElement.style.display = 'none';
            document.body.appendChild(testElement);

            const computedStyle = window.getComputedStyle(testElement);
            const isLoaded = computedStyle.backgroundColor === 'rgb(59, 130, 246)';

            document.body.removeChild(testElement);

            if (!isLoaded) {
                console.warn('⚠️ Tailwind CSS not loaded properly, applying fallback styles');
                document.body.className = 'fallback-styles';
                const container = document.querySelector('.min-h-screen');
                if (container) {
                    container.className = 'fallback-container';
                }
            }

            return isLoaded;
        }

        // Check for missing icons and provide fallbacks
        function handleMissingIcons() {
            const icons = document.querySelectorAll('[data-lucide]');
            icons.forEach(icon => {
                const iconName = icon.getAttribute('data-lucide');
                // Add error handling for missing icons
                icon.onerror = function() {
                    console.warn(`Icon ${iconName} not found, using fallback`);
                    this.innerHTML = '●'; // Simple fallback
                };
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Check if Tailwind loaded
            setTimeout(checkTailwindLoaded, 100);

            // Handle missing icons
            handleMissingIcons();

            // Initialize Lucide icons with error handling
            try {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                } else {
                    console.warn('⚠️ Lucide icons not loaded, using fallbacks');
                }
            } catch (error) {
                console.warn('⚠️ Error initializing icons:', error);
            }

            // Hide loading indicator
            setTimeout(() => {
                const loadingIndicator = document.getElementById('loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.opacity = '0';
                    loadingIndicator.style.transition = 'opacity 0.3s ease';
                    setTimeout(() => {
                        loadingIndicator.style.display = 'none';
                    }, 300);
                }
            }, 500);

            const steps = document.querySelectorAll('.step-section');
            const stepIndicatorsContainer = document.getElementById('step-indicators-container');
            const nextButtons = document.querySelectorAll('.next-btn');
            const prevButtons = document.querySelectorAll('.prev-btn');
            const form = document.getElementById('signup-form');
            const progressText = document.getElementById('progress-text');
            let currentStep = 0;

            const stepConfig = [
                { id: 'account', label: 'Account'},
                { id: 'business', label: 'Business & Cuisine'},
                { id: 'operations', label: 'Operations'},
                { id: 'tables', label: 'Tables'},
                { id: 'summary', label: 'Review'},
                { id: 'personal', label: 'Personal'}
            ];

            // Validation rules (Updated for UK)
            const validationRules = {
                email: { required: true, pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: 'Please enter a valid email address' },
                password: { required: true, minLength: 8, message: 'Password must be at least 8 characters long' },
                businessName: { required: true, minLength: 2, message: 'Business name must be at least 2 characters' },
                businessType: { required: true, message: 'Please select a business type' },
                firstName: { required: true, minLength: 2, message: 'First name must be at least 2 characters' },
                lastName: { required: true, minLength: 2, message: 'Last name must be at least 2 characters' },
                phone: { required: true, pattern: /^(?:(?:\(?(?:0(?:0|11)\)?[\s-]?\(?|0)44\)?[\s-]?(?:\(?0\)?[\s-]?)?)|(?:\(?0))(?:(?:\d{5}\)?[\s-]?\d{4,5})|(?:\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3}))|(?:\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4})|(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}))(?:[\s-]?\d{4})?$/, message: 'Please enter a valid UK phone number' },
                address: { required: true, minLength: 5, message: 'Please enter a valid address' },
                city: { required: true, minLength: 2, message: 'Please enter a valid city' },
                postcode: { required: true, pattern: /^([Gg][Ii][Rr] 0[Aa]{2})|((([A-Za-z][0-9]{1,2})|(([A-Za-z][A-Ha-hJ-Yj-y][0-9]{1,2})|(([A-Za-z][0-9][A-Za-z])|([A-Za-z][A-Ha-hJ-Yj-y][0-9][A-Za-z]?))))\s?[0-9][A-Za-z]{2})$/, message: 'Please enter a valid UK postcode' }
            };

            // Form validation
            const validateField = (field) => {
                const fieldName = field.name;
                const fieldValue = field.value.trim();
                const rules = validationRules[fieldName];
                if (!rules) return true;
                const fieldGroup = field.closest('.form-group');
                const validationMessage = fieldGroup ? fieldGroup.querySelector('.validation-message') : null;
                field.classList.remove('error', 'success');
                if (validationMessage) validationMessage.innerHTML = '';
                if (rules.required && fieldValue === '') {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> This field is required</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.minLength && fieldValue.length < rules.minLength) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                if (rules.pattern && !rules.pattern.test(fieldValue)) {
                    field.classList.add('error');
                    if (validationMessage) validationMessage.innerHTML = `<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> ${rules.message}</div>`;
                    lucide.createIcons();
                    return false;
                }
                field.classList.add('success');
                if (validationMessage) validationMessage.innerHTML = `<div class="success-message"><i data-lucide="check-circle" class="w-3 h-3"></i> Looks good!</div>`;
                lucide.createIcons();
                return true;
            };

            const validateStep = (stepIndex) => {
                const currentStepElement = steps[stepIndex];
                const fields = currentStepElement.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                fields.forEach(field => {
                    if (!field.disabled && !validateField(field)) isValid = false;
                });
                if (stepIndex === 3) { // Table setup is now step 4 (index 3)
                    const layoutStyle = form.querySelector('input[name="layoutStyle"]:checked').value;
                    const tableCount = (layoutStyle === 'section') 
                        ? document.querySelectorAll('#table-sections-container .table-row').length
                        : document.querySelectorAll('#flow-table-list .table-row').length;

                    if (tableCount === 0) {
                        alert('Please add at least one table before continuing.');
                        return false;
                    }
                }
                return isValid;
            };

            // Update progress indicators and progress bar
            const updateProgress = () => {
                progressText.textContent = `Step ${currentStep + 1} of ${stepConfig.length} - ${stepConfig[currentStep].label}`;
                stepIndicatorsContainer.innerHTML = '';
                stepConfig.forEach((step, index) => {
                    const isCompleted = index < currentStep;
                    const isActive = index === currentStep;
                    
                    const indicator = document.createElement('div');
                    indicator.className = 'step-indicator flex-1 flex flex-col items-center relative';
                    if(isCompleted) indicator.classList.add('completed');
                    if(isActive) indicator.classList.add('active');

                    let statusIcon = isCompleted ? '<i data-lucide="check" class="w-4 h-4"></i>' : `${index + 1}`;
                    
                    indicator.innerHTML = `
                        <div class="step-circle relative z-10 w-8 h-8 rounded-full flex items-center justify-center font-bold border-2 border-slate-300 bg-white flex-shrink-0">${statusIcon}</div>
                        <div class="step-label text-xs text-center mt-2 text-slate-500">${step.label}</div>
                    `;
                    
                    stepIndicatorsContainer.appendChild(indicator);

                    if (index < stepConfig.length - 1) {
                        const connector = document.createElement('div');
                        connector.className = 'step-connector h-1 w-full absolute top-4 left-1/2 transform -translate-y-1/2';
                        indicator.appendChild(connector);
                    }
                });
                lucide.createIcons();
            };

            // Show a specific step
            const showStep = (stepIndex, isNavigatingBack = false) => {
                if (!isNavigatingBack && stepIndex > currentStep && !validateStep(currentStep)) {
                    return;
                }
                const currentButton = document.querySelector(`#step-${currentStep + 1} .next-btn`);
                if (currentButton && !isNavigatingBack && stepIndex > currentStep) {
                    const btnText = currentButton.querySelector('.btn-text');
                    const btnLoading = currentButton.querySelector('.btn-loading');
                    btnText.classList.add('hidden');
                    btnLoading.classList.remove('hidden');
                    setTimeout(() => {
                        btnText.classList.remove('hidden');
                        btnLoading.classList.add('hidden');
                        performStepChange(stepIndex);
                    }, 800);
                } else {
                    performStepChange(stepIndex);
                }
            };

            const performStepChange = (stepIndex) => {
                if (stepIndex === 4) generateSummary(); // Summary is now step 5 (index 4)
                steps.forEach((step, index) => step.classList.toggle('active', index === stepIndex));
                currentStep = stepIndex;
                updateProgress();
            };

            nextButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.next))));
            prevButtons.forEach(button => button.addEventListener('click', () => showStep(parseInt(button.dataset.prev), true)));
            form.addEventListener('input', (e) => { if (e.target.matches('input, select, textarea')) validateField(e.target); });

            // Password strength and toggle
            const passwordField = form.querySelector('input[name="password"]');
            const passwordStrength = passwordField.closest('.form-group').querySelector('.password-strength');
            passwordField.addEventListener('input', () => {
                const password = passwordField.value;
                const strengthBars = passwordStrength.querySelectorAll('.strength-bar');
                const strengthText = passwordStrength.querySelector('.strength-text');
                if (password.length === 0) { passwordStrength.classList.add('hidden'); return; }
                passwordStrength.classList.remove('hidden');
                let strength = 0;
                if (password.length >= 8) strength++; if (/[A-Z]/.test(password)) strength++; if (/[a-z]/.test(password)) strength++; if (/[0-9]/.test(password)) strength++; if (/[^A-Za-z0-9]/.test(password)) strength++;
                const strengthLevels = { 1: 'bg-red-400', 2: 'bg-red-400', 3: 'bg-yellow-400', 4: 'bg-blue-400', 5: 'bg-green-400' };
                const strengthLabels = { 1: 'Very Weak', 2: 'Weak', 3: 'Fair', 4: 'Good', 5: 'Strong' };
                strengthBars.forEach((bar, i) => bar.className = `strength-bar h-1 rounded flex-1 ${i < strength ? strengthLevels[strength] : 'bg-slate-200'}`);
                strengthText.textContent = `Password strength: ${strengthLabels[strength] || ''}`;
            });
            form.querySelector('.toggle-password').addEventListener('click', (e) => {
                const btn = e.currentTarget;
                const icon = btn.querySelector('i');
                const isPassword = passwordField.type === 'password';
                passwordField.type = isPassword ? 'text' : 'password';
                icon.setAttribute('data-lucide', isPassword ? 'eye-off' : 'eye');
                lucide.createIcons();
            });

            // Logo upload
            const logoUpload = document.getElementById('logo-upload');
            if(logoUpload) {
                const logoFile = document.getElementById('logo-file');
                const logoPreview = document.getElementById('logo-preview');
                logoUpload.addEventListener('click', () => logoFile.click());
                logoUpload.addEventListener('dragover', (e) => { e.preventDefault(); logoUpload.classList.add('dragover'); });
                logoUpload.addEventListener('dragleave', () => logoUpload.classList.remove('dragover'));
                logoUpload.addEventListener('drop', (e) => { e.preventDefault(); logoUpload.classList.remove('dragover'); if (e.dataTransfer.files.length) handleLogoFile(e.dataTransfer.files[0]); });
                logoFile.addEventListener('change', (e) => { if (e.target.files.length) handleLogoFile(e.target.files[0]); });
                const handleLogoFile = (file) => {
                    if (!file.type.startsWith('image/')) return;
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        logoPreview.querySelector('img').src = e.target.result;
                        logoPreview.querySelector('#logo-name').textContent = file.name;
                        logoPreview.querySelector('#logo-size').textContent = `${(file.size / 1024).toFixed(1)} KB`;
                        logoPreview.classList.remove('hidden');
                        logoUpload.classList.add('hidden');
                    };
                    reader.readAsDataURL(file);
                };
                document.getElementById('remove-logo').addEventListener('click', () => { logoFile.value = ''; logoPreview.classList.add('hidden'); logoUpload.classList.remove('hidden'); });
            }
            
            // --- Operations & Daily Revenue Logic ---
            const operationsContainer = document.getElementById('operations-schedule-container');
            let scheduleData = [];

            const daysOfWeekOps = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
            const lunchTillOptions = ['<300', '300-600', '600-1000', '1000+'];
            const dinnerTillOptions = ['<500', '500-1000', '1000-1500', '1500-2000', '2000+'];

            const createInitialStaffing = () => [
              { id: 1, name: 'Head Chef', count: 0, category: 'Kitchen', isCustom: false },
              { id: 2, name: 'Sous Chef', count: 0, category: 'Kitchen', isCustom: false },
              { id: 3, name: 'Line Chefs', count: 0, category: 'Kitchen', isCustom: false },
              { id: 4, name: 'Managers', count: 0, category: 'Front of House', isCustom: false },
              { id: 5, name: 'Waiters', count: 0, category: 'Front of House', isCustom: false },
              { id: 6, name: 'Food Runners', count: 0, category: 'Front of House', isCustom: false },
              { id: 7, name: 'Bartenders', count: 0, category: 'Front of House', isCustom: false },
              { id: 8, name: 'Cleaners', count: 0, category: 'Front of House', isCustom: false },
            ];

            const createInitialDay = (dayName) => ({
              dayName,
              openingTime: "10:00",
              closingTime: "23:00",
              services: [
                {
                  name: "Lunch",
                  isActive: true,
                  startTime: "12:00",
                  endTime: "15:00",
                  averageTill: lunchTillOptions[0],
                  staff: createInitialStaffing(),
                },
                {
                  name: "Dinner",
                  isActive: true,
                  startTime: "17:30",
                  endTime: "22:00",
                  averageTill: dinnerTillOptions[0],
                  staff: createInitialStaffing(),
                },
              ],
            });
            
            scheduleData = daysOfWeekOps.map(createInitialDay);
            
            function renderOperationsSchedule() {
                operationsContainer.innerHTML = scheduleData.map((day, dayIndex) => `
                    <div class="day-card bg-white border border-slate-200 rounded-xl shadow-sm p-6">
                        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 pb-4 border-b border-slate-200">
                            <h3 class="text-2xl font-bold text-slate-800 mb-2 sm:mb-0">${day.dayName}</h3>
                            <div class="flex items-center space-x-2">
                                <label class="text-sm font-medium text-slate-700 whitespace-nowrap">Restaurant Hours:</label>
                                <input type="time" value="${day.openingTime}" data-day-index="${dayIndex}" data-field="openingTime" class="day-time-input w-full p-1 border border-slate-300 rounded-md" />
                                <span class="text-slate-500">-</span>
                                <input type="time" value="${day.closingTime}" data-day-index="${dayIndex}" data-field="closingTime" class="day-time-input w-full p-1 border border-slate-300 rounded-md" />
                            </div>
                        </div>
                        ${day.services.map((service, serviceIndex) => renderService(service, dayIndex, serviceIndex)).join('')}
                    </div>
                `).join('');
                lucide.createIcons();
            }
            
            function renderService(service, dayIndex, serviceIndex) {
                 const tillOptions = service.name === 'Lunch' ? lunchTillOptions : dinnerTillOptions;
                 const kitchenStaff = service.staff.filter(s => s.category === 'Kitchen');
                 const fohStaff = service.staff.filter(s => s.category === 'Front of House');

                return `
                <div class="service-editor p-4 rounded-lg mt-4 ${service.isActive ? 'bg-white' : 'bg-slate-100 opacity-60'}">
                    <div class="flex items-center justify-between pb-3 border-b border-slate-200">
                        <h4 class="font-semibold text-lg text-slate-800">${service.name} Service</h4>
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" ${service.isActive ? 'checked' : ''} data-day-index="${dayIndex}" data-service-index="${serviceIndex}" class="service-active-toggle sr-only peer" />
                            <div class="relative w-11 h-6 bg-slate-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-slate-600"></div>
                        </label>
                    </div>
                    <div class="service-details transition-all duration-300 ease-in-out grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 ${!service.isActive ? 'max-h-0 overflow-hidden opacity-0' : 'max-h-full opacity-100'}">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">Service Hours</label>
                                <div class="flex items-center space-x-2">
                                    <input type="time" value="${service.startTime}" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-field="startTime" class="service-input w-full p-1 border border-slate-300 rounded-md" />
                                    <span class="text-slate-500">-</span>
                                    <input type="time" value="${service.endTime}" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-field="endTime" class="service-input w-full p-1 border border-slate-300 rounded-md" />
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-slate-700 mb-1">Average Till (£)</label>
                                <select value="${service.averageTill}" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-field="averageTill" class="service-input w-full p-1.5 border border-slate-300 rounded-md bg-white">
                                    ${tillOptions.map(opt => `<option key="${opt}" value="${opt}">${opt}</option>`).join('')}
                                </select>
                            </div>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-slate-700 mb-2">Staffing Levels</label>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-x-6">
                                <div>
                                    <h5 class="font-semibold text-slate-700 mt-2 border-b border-slate-200 pb-1 mb-2">Kitchen</h5>
                                    ${kitchenStaff.map(staff => renderStaffInput(staff, dayIndex, serviceIndex)).join('')}
                                    <button type="button" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-category="Kitchen" class="add-staff-btn text-sm text-slate-500 hover:text-slate-800 mt-3 flex items-center"><i data-lucide="plus" class="w-4 h-4 mr-1"></i> Add Staff Role</button>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-slate-700 mt-2 border-b border-slate-200 pb-1 mb-2">Front of House</h5>
                                    ${fohStaff.map(staff => renderStaffInput(staff, dayIndex, serviceIndex)).join('')}
                                    <button type="button" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-category="Front of House" class="add-staff-btn text-sm text-slate-500 hover:text-slate-800 mt-3 flex items-center"><i data-lucide="plus" class="w-4 h-4 mr-1"></i> Add Staff Role</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            }

            function renderStaffInput(staff, dayIndex, serviceIndex) {
                return `
                <div class="flex items-center justify-between mt-2 space-x-2">
                    <input type="text" value="${staff.name}" ${!staff.isCustom ? 'readonly' : ''} data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-staff-id="${staff.id}" data-field="name" class="staff-input flex-grow p-1 rounded-md text-sm ${staff.isCustom ? 'bg-slate-100 border border-slate-300' : 'bg-transparent border-transparent text-slate-600'}" placeholder="Role Name" />
                    <input type="number" min="0" value="${staff.count}" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-staff-id="${staff.id}" data-field="count" class="staff-input w-16 p-1 text-center bg-slate-100 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-400" />
                    <div class="w-6 h-6 flex items-center justify-center">
                        ${staff.isCustom ? `<button type="button" data-day-index="${dayIndex}" data-service-index="${serviceIndex}" data-staff-id="${staff.id}" class="delete-staff-btn p-1 rounded-full hover:bg-red-100 transition-colors"><i data-lucide="trash-2" class="w-4 h-4 text-slate-400 hover:text-red-500"></i></button>` : ''}
                    </div>
                </div>
                `;
            }
            
            operationsContainer.addEventListener('change', (e) => {
                const target = e.target;
                const dayIndex = parseInt(target.dataset.dayIndex);
                
                if(target.classList.contains('day-time-input')) {
                    scheduleData[dayIndex][target.dataset.field] = target.value;
                } else if (target.classList.contains('service-active-toggle')) {
                    const serviceIndex = parseInt(target.dataset.serviceIndex);
                    scheduleData[dayIndex].services[serviceIndex].isActive = target.checked;
                    renderOperationsSchedule(); // Re-render to show/hide details
                } else if (target.classList.contains('service-input')) {
                    const serviceIndex = parseInt(target.dataset.serviceIndex);
                    scheduleData[dayIndex].services[serviceIndex][target.dataset.field] = target.value;
                } else if (target.classList.contains('staff-input')) {
                    const serviceIndex = parseInt(target.dataset.serviceIndex);
                    const staffId = parseInt(target.dataset.staffId);
                    const field = target.dataset.field;
                    const value = field === 'count' ? parseInt(target.value) || 0 : target.value;
                    const staffMember = scheduleData[dayIndex].services[serviceIndex].staff.find(s => s.id === staffId);
                    if(staffMember) staffMember[field] = value;
                }
            });

            operationsContainer.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if(!button) return;

                if (button.classList.contains('add-staff-btn')) {
                    const dayIndex = parseInt(button.dataset.dayIndex);
                    const serviceIndex = parseInt(button.dataset.serviceIndex);
                    const category = button.dataset.category;
                    const newRole = { id: Date.now(), name: '', count: 1, category, isCustom: true };
                    scheduleData[dayIndex].services[serviceIndex].staff.push(newRole);
                    renderOperationsSchedule();
                } else if (button.classList.contains('delete-staff-btn')) {
                    const dayIndex = parseInt(button.dataset.dayIndex);
                    const serviceIndex = parseInt(button.dataset.serviceIndex);
                    const staffId = parseInt(button.dataset.staffId);
                    const staffList = scheduleData[dayIndex].services[serviceIndex].staff;
                    scheduleData[dayIndex].services[serviceIndex].staff = staffList.filter(s => s.id !== staffId);
                    renderOperationsSchedule();
                }
            });

            renderOperationsSchedule();


            // --- Table Configuration Logic ---
            const tableSetupStep = document.getElementById('step-4');
            const layoutStyleRadios = tableSetupStep.querySelectorAll('input[name="layoutStyle"]');
            const sectionUI = tableSetupStep.querySelector('#section-based-ui');
            const flowUI = tableSetupStep.querySelector('#flow-based-ui');
            const floorPlanPreview = tableSetupStep.querySelector('#floor-plan-preview');
            const sectionsContainer = tableSetupStep.querySelector('#table-sections-container');
            const addFlowTableBtn = tableSetupStep.querySelector('#add-flow-table-btn');
            const flowTableList = tableSetupStep.querySelector('#flow-table-list');
            const addSectionModal = document.getElementById('add-section-modal');
            const showAddSectionModalBtn = tableSetupStep.querySelector('#show-add-section-modal-btn');
            const cancelAddSectionBtn = document.getElementById('cancel-add-section');
            const confirmAddSectionBtn = document.getElementById('confirm-add-section');
            const modalSectionNameInput = document.getElementById('modal-section-name');
            let tableIdCounter = 0;
            let sectionIdCounter = 0;
            
            layoutStyleRadios.forEach(radio => {
                radio.addEventListener('change', () => {
                    const isSection = radio.value === 'section';
                    sectionUI.classList.toggle('hidden', !isSection);
                    flowUI.classList.toggle('hidden', isSection);
                    updateFloorPlan();
                });
            });

            showAddSectionModalBtn.addEventListener('click', () => addSectionModal.classList.remove('hidden'));
            cancelAddSectionBtn.addEventListener('click', () => addSectionModal.classList.add('hidden'));
            confirmAddSectionBtn.addEventListener('click', () => {
                const name = modalSectionNameInput.value.trim();
                if (name) {
                    addSection(name);
                    modalSectionNameInput.value = '';
                    addSectionModal.classList.add('hidden');
                }
            });

            function addSection(name) {
                sectionIdCounter++;
                const sectionId = `section-${sectionIdCounter}`;
                const sectionDiv = document.createElement('div');
                sectionDiv.className = 'table-section p-4 bg-white border border-slate-200 rounded-lg';
                sectionDiv.dataset.sectionId = sectionId;
                sectionDiv.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-semibold text-slate-800">${name}</h4>
                        <div class="flex items-center gap-2">
                           <label class="text-xs text-slate-500">Tables:</label>
                           <input type="number" value="0" min="0" class="table-count-input form-input w-20 text-sm p-1">
                        </div>
                    </div>
                    <div class="table-list-in-section space-y-2"></div>
                `;
                sectionsContainer.appendChild(sectionDiv);
                
                const tableCountInput = sectionDiv.querySelector('.table-count-input');
                const tableContainer = sectionDiv.querySelector('.table-list-in-section');

                tableCountInput.addEventListener('input', (e) => {
                    const targetCount = parseInt(e.target.value) || 0;
                     if (targetCount < 0) {
                        e.target.value = 0;
                        return;
                    }
                    const currentCount = tableContainer.children.length;

                    if (targetCount > currentCount) {
                        for (let i = 0; i < targetCount - currentCount; i++) {
                            addTableRowToContainer(tableContainer);
                        }
                    } else if (targetCount < currentCount) {
                        for (let i = 0; i < currentCount - targetCount; i++) {
                            if (tableContainer.lastChild) {
                                tableContainer.lastChild.remove();
                            }
                        }
                    }
                    updateFloorPlan();
                });
                
                updateFloorPlan();
            }
            
            function addTableRowToContainer(container, capacity = 4, type = 'Indoor') {
                tableIdCounter++;
                const tableRow = document.createElement('div');
                tableRow.className = 'table-row grid grid-cols-12 items-center gap-2 p-2 rounded-lg bg-slate-50';
                tableRow.innerHTML = `
                    <input type="text" value="T${tableIdCounter}" name="tableName" class="col-span-4 form-input text-sm p-1">
                    <input type="number" value="${capacity}" min="1" name="tableCapacity" class="col-span-3 form-input text-sm p-1">
                    <select name="tableType" class="col-span-4 form-input text-sm p-1">
                        <option ${type === 'Indoor' ? 'selected' : ''}>Indoor</option>
                        <option ${type === 'Outdoor' ? 'selected' : ''}>Outdoor</option>
                        <option ${type === 'Bar' ? 'selected' : ''}>Bar</option>
                    </select>
                    <button type="button" class="remove-table-btn text-slate-400 hover:text-red-500 col-span-1"><i data-lucide="x" class="w-4 h-4"></i></button>
                `;
                container.appendChild(tableRow);
                lucide.createIcons();
                tableRow.addEventListener('input', updateFloorPlan);
                tableRow.querySelector('.remove-table-btn').addEventListener('click', () => {
                    tableRow.remove();
                    // Adjust the parent section's count input
                    const section = tableRow.closest('.table-section');
                    if(section){
                        const countInput = section.querySelector('.table-count-input');
                        countInput.value = parseInt(countInput.value) - 1;
                    }
                    updateFloorPlan();
                });
                updateFloorPlan();
            }
            
            addFlowTableBtn.addEventListener('click', () => addTableRowToContainer(flowTableList));

            function updateFloorPlan() {
                floorPlanPreview.innerHTML = '';
                const layoutStyle = form.querySelector('input[name="layoutStyle"]:checked').value;

                if (layoutStyle === 'section') {
                    floorPlanPreview.innerHTML = '<div id="floor-plan-sections"></div>';
                    const floorPlanSectionsContainer = document.getElementById('floor-plan-sections');
                    const sections = sectionsContainer.querySelectorAll('.table-section');
                    if(sections.length === 0) {
                         floorPlanSectionsContainer.innerHTML = `<p class="text-center text-slate-400">Add sections and tables to see a preview</p>`;
                         return;
                    }
                    sections.forEach(section => {
                        const sectionName = section.querySelector('h4').textContent;
                        const sectionPreview = document.createElement('div');
                        sectionPreview.className = 'floor-plan-section';
                        sectionPreview.innerHTML = `<h5 class="floor-plan-section-title">${sectionName}</h5><div class="floor-plan-table-grid"></div>`;
                        const grid = sectionPreview.querySelector('.floor-plan-table-grid');
                        
                        section.querySelectorAll('.table-row').forEach(tableRow => {
                             const tableElement = createTableElement(tableRow);
                             grid.appendChild(tableElement);
                        });
                        floorPlanSectionsContainer.appendChild(sectionPreview);
                    });

                } else { // Flow layout
                    floorPlanPreview.innerHTML = '<div class="floor-plan-table-grid"></div>';
                    const grid = floorPlanPreview.querySelector('.floor-plan-table-grid');
                    const tables = flowTableList.querySelectorAll('.table-row');
                    if(tables.length === 0) {
                         grid.innerHTML = `<p class="col-span-full text-center text-slate-400">Add tables to see a preview</p>`;
                         return;
                    }
                    tables.forEach(tableRow => {
                        const tableElement = createTableElement(tableRow);
                        grid.appendChild(tableElement);
                    });
                }
            }
            
            function createTableElement(tableRow) {
                const name = tableRow.querySelector('input[name="tableName"]').value;
                const capacity = tableRow.querySelector('input[name="tableCapacity"]').value;
                const type = tableRow.querySelector('select[name="tableType"]').value;
                const colors = { Indoor: 'bg-blue-100 border-blue-300 text-blue-800', Outdoor: 'bg-green-100 border-green-300 text-green-800', Bar: 'bg-yellow-100 border-yellow-300 text-yellow-800' };
                const tableElement = document.createElement('div');
                tableElement.className = `border-2 rounded-lg p-2 text-center text-xs font-medium ${colors[type]}`;
                tableElement.innerHTML = `<div class="font-bold text-sm">${name}</div><div class="text-slate-600">${capacity} seats</div>`;
                return tableElement;
            }


            // Enhanced Summary Generation
            const generateSummary = () => {
                const summaryContainer = document.getElementById('summary-container');
                const formElements = form.elements;
                
                const data = {
                    email: formElements.email.value,
                    businessName: formElements.businessName.value,
                    businessType: formElements.businessType.value,
                    cuisines: Array.from(form.querySelectorAll('input[name="cuisine"]:checked')).map(el => el.value).join(', ') || 'Not specified',
                    operations: scheduleData,
                    tables: Array.from(document.querySelectorAll('#step-4 .table-row')).map(row => ({
                        name: row.querySelector('input[name="tableName"]').value,
                        capacity: row.querySelector('input[name="tableCapacity"]').value,
                        type: row.querySelector('select[name="tableType"]').value,
                        section: row.closest('.table-section')?.querySelector('h4')?.textContent || 'Flow'
                    }))
                };
                
                let tablesHtml = data.tables.map(t => `<li class="flex justify-between py-2 px-3 bg-white rounded-lg border border-slate-200"><span class="font-medium">${t.name} (${t.type} / ${t.section})</span><span class="text-slate-500">${t.capacity} seats</span></li>`).join('');
                if (!tablesHtml) tablesHtml = '<li class="text-slate-400 italic">No tables configured.</li>';
                const totalCapacity = data.tables.reduce((sum, t) => sum + parseInt(t.capacity || 0), 0);

                let opsHtml = data.operations.map(day => `
                    <div class="mt-4">
                        <p class="font-bold text-slate-700">${day.dayName}: ${day.openingTime} - ${day.closingTime}</p>
                        <ul class="pl-4 text-xs">
                        ${day.services.filter(s => s.isActive).map(s => `
                            <li>${s.name}: ${s.startTime}-${s.endTime} (Avg Till: ${s.averageTill})</li>
                        `).join('')}
                        </ul>
                    </div>
                `).join('');

                summaryContainer.innerHTML = `
                    <div class="space-y-6">
                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="user-check" class="w-5 h-5 text-blue-600"></i> Account & Business</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div><p class="text-xs text-slate-500 uppercase">Email</p><p class="font-semibold text-slate-700">${data.email}</p></div>
                                <div><p class="text-xs text-slate-500 uppercase">Business Name</p><p class="font-semibold text-slate-700">${data.businessName}</p></div>
                                <div><p class="text-xs text-slate-500 uppercase">Business Type</p><p class="font-semibold text-slate-700">${data.businessType}</p></div>
                                <div><p class="text-xs text-slate-500 uppercase">Cuisines</p><p class="font-semibold text-slate-700">${data.cuisines}</p></div>
                            </div>
                        </div>
                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="clock" class="w-5 h-5 text-green-600"></i> Operations & Revenue</h4>
                            ${opsHtml}
                        </div>
                        <div class="bg-white p-6 rounded-xl border border-slate-200">
                            <h4 class="font-bold text-slate-800 flex items-center gap-2 mb-4"><i data-lucide="layout" class="w-5 h-5 text-purple-600"></i> Table Configuration</h4>
                            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                                <div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${data.tables.length}</p><p class="text-xs text-slate-500">Total Tables</p></div>
                                <div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${totalCapacity}</p><p class="text-xs text-slate-500">Total Seats</p></div>
                                <div class="text-center p-3 bg-slate-50 rounded-lg"><p class="text-2xl font-bold text-slate-800">${data.tables.length > 0 ? (totalCapacity / data.tables.length).toFixed(1) : 0}</p><p class="text-xs text-slate-500">Avg. per Table</p></div>
                            </div>
                            <ul class="space-y-2">${tablesHtml}</ul>
                        </div>
                    </div>`;
                lucide.createIcons();
            };

            // Form submission
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                if (!validateStep(currentStep)) return;
                if (!document.getElementById('terms-checkbox').checked) {
                    alert('Please accept the Terms of Service and Privacy Policy.');
                    return;
                }
                const submitBtn = form.querySelector('button[type="submit"]');
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
                setTimeout(() => {
                    const userData = {
                        email: form.elements.email.value,
                        businessName: form.elements.businessName.value,
                        firstName: form.elements.firstName.value,
                        signupDate: new Date().toISOString(),
                    };
                    localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                    window.dispatchEvent(new CustomEvent('userRegistered', { detail: userData }));
                    const successModal = document.createElement('div');
                    successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                    successModal.innerHTML = `
                        <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"><i data-lucide="check" class="w-8 h-8 text-green-600"></i></div>
                            <h3 class="text-2xl font-bold text-slate-800 mb-2">Account Created!</h3>
                            <p class="text-slate-600 mb-6">Welcome to RestroManager, ${userData.firstName}!</p>
                            <button id="redirect-dashboard" class="btn-primary w-full py-3 text-white rounded-xl font-semibold">Go to Dashboard</button>
                            <p class="text-xs text-slate-400 mt-3">Redirecting in <span id="countdown">5</span>s...</p>
                        </div>`;
                    document.body.appendChild(successModal);
                    lucide.createIcons();
                    let countdown = 5;
                    const countdownEl = document.getElementById('countdown');
                    const interval = setInterval(() => {
                        countdown--;
                        countdownEl.textContent = countdown;
                        if (countdown <= 0) {
                            clearInterval(interval);
                            console.log("Redirecting to dashboard..."); // window.location.href = 'dashboard.html';
                        }
                    }, 1000);
                    document.getElementById('redirect-dashboard').addEventListener('click', () => {
                        clearInterval(interval);
                        console.log("Redirecting to dashboard..."); // window.location.href = 'dashboard.html';
                    });
                }, 2000);
            });

            // Initialize
            updateProgress();
            addSection('Main Dining'); // Add a default section
        });
    </script>
</body>
</html>
