<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - RestroManager</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="Complete restaurant management solution for orders, menu, tasks, and analytics">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="RestroManager">
    <meta name="msapplication-TileColor" content="#2563eb">
    <meta name="msapplication-config" content="./browserconfig.xml">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="./manifest.json">
    
    <!-- PWA Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="./icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="./icons/icon-72x72.png">
    <link rel="apple-touch-icon" sizes="180x180" href="./icons/icon-192x192.png">
    <link rel="mask-icon" href="./icons/icon-192x192.png" color="#2563eb">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest"></script>
    
    <!-- PWA Utils -->
    <script src="./pwa-utils.js"></script>
    
    <style>
        :root {
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --success-500: #10b981;
            --error-500: #ef4444;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        
        .login-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }
        
        .form-input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .form-input:focus {
            transform: translateY(-1px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
        
        .form-input.success {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .role-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .role-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
        }
        
        .role-card.selected {
            border-color: #2563eb;
            background: linear-gradient(135deg, #eff6ff, #dbeafe);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
        }
        
        .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }
        
        .success-message {
            color: #10b981;
            font-size: 0.75rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            animation: slideDown 0.3s ease-out;
        }
        
        @keyframes slideDown {
            from { 
                opacity: 0; 
                transform: translateY(-10px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .login-container {
                margin: 0.5rem;
                border-radius: 1rem;
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md login-container rounded-2xl overflow-hidden my-8">
        <!-- Header -->
        <div class="p-8 pb-6 text-center">
            <a href="#" class="flex items-center justify-center gap-3 mb-8">
                <div class="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg">
                    <i data-lucide="chef-hat" class="w-8 h-8 text-white"></i>
                </div>
                <h1 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent">RestroManager</h1>
            </a>
            
            <div class="mb-6">
                <h2 class="text-2xl font-bold bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent mb-2">Welcome Back</h2>
                <p class="text-slate-500">Sign in to your restaurant dashboard</p>
            </div>
        </div>
        
        <!-- Login Form -->
        <div class="px-8 pb-8">
            <form id="login-form">
                <!-- Role Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-semibold text-slate-700 mb-3">Login as</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div class="role-card border-2 border-slate-200 rounded-xl p-4 text-center" data-role="admin">
                            <div class="text-2xl mb-2">👨‍💼</div>
                            <div class="text-sm font-semibold text-slate-700">Admin</div>
                            <div class="text-xs text-slate-500">Full Access</div>
                        </div>
                        <div class="role-card border-2 border-slate-200 rounded-xl p-4 text-center" data-role="manager">
                            <div class="text-2xl mb-2">👩‍💼</div>
                            <div class="text-sm font-semibold text-slate-700">Manager</div>
                            <div class="text-xs text-slate-500">Operations</div>
                        </div>
                        <div class="role-card border-2 border-slate-200 rounded-xl p-4 text-center" data-role="staff">
                            <div class="text-2xl mb-2">👨‍🍳</div>
                            <div class="text-sm font-semibold text-slate-700">Staff</div>
                            <div class="text-xs text-slate-500">Daily Tasks</div>
                        </div>
                        <div class="role-card border-2 border-slate-200 rounded-xl p-4 text-center" data-role="waiter">
                            <div class="text-2xl mb-2">🧑‍🍳</div>
                            <div class="text-sm font-semibold text-slate-700">Waiter</div>
                            <div class="text-xs text-slate-500">Orders</div>
                        </div>
                    </div>
                    <input type="hidden" id="selected-role" name="role" required>
                    <div class="validation-message"></div>
                </div>
                
                <!-- Email Input -->
                <div class="form-group mb-4">
                    <label for="email" class="block text-sm font-semibold text-slate-700 mb-2">Email Address</label>
                    <div class="relative">
                        <i data-lucide="mail" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                        <input type="email" id="email" name="email" required 
                               class="form-input pl-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter your email">
                    </div>
                    <div class="validation-message"></div>
                </div>
                
                <!-- Password Input -->
                <div class="form-group mb-6">
                    <label for="password" class="block text-sm font-semibold text-slate-700 mb-2">Password</label>
                    <div class="relative">
                        <i data-lucide="lock" class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400 transition-colors"></i>
                        <input type="password" id="password" name="password" required 
                               class="form-input pl-12 pr-12 block w-full px-4 py-4 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter your password">
                        <button type="button" class="toggle-password absolute right-4 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 transition-colors">
                            <i data-lucide="eye" class="w-5 h-5"></i>
                        </button>
                    </div>
                    <div class="validation-message"></div>
                </div>
                
                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" id="remember-me" class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500">
                        <span class="ml-2 text-sm text-slate-600">Remember me</span>
                    </label>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-800 font-medium">Forgot password?</a>
                </div>
                
                <!-- Login Button -->
                <button type="submit" class="btn-primary w-full px-6 py-4 text-sm font-semibold text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <span class="btn-text">Sign In</span>
                    <span class="btn-loading hidden">
                        <span class="loading-spinner"></span>
                        Signing in...
                    </span>
                </button>
                
                <!-- Divider -->
                <div class="flex items-center my-6">
                    <div class="flex-grow border-t border-slate-200"></div>
                    <span class="flex-shrink mx-4 text-slate-400 text-sm font-medium">OR</span>
                    <div class="flex-grow border-t border-slate-200"></div>
                </div>
                
                <!-- Sign Up Link -->
                <div class="text-center">
                    <p class="text-sm text-slate-600">
                        Don't have an account? 
                        <a href="./signup.html" class="text-blue-600 hover:text-blue-800 font-semibold">Create one here</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            lucide.createIcons();

            const form = document.getElementById('login-form');
            const roleCards = document.querySelectorAll('.role-card');
            const selectedRoleInput = document.getElementById('selected-role');
            const togglePasswordBtn = document.querySelector('.toggle-password');
            const passwordField = document.getElementById('password');

            // Demo user accounts for different roles
            const demoAccounts = {
                admin: { email: '<EMAIL>', password: 'admin123', name: 'Admin User' },
                manager: { email: '<EMAIL>', password: 'manager123', name: 'Manager User' },
                staff: { email: '<EMAIL>', password: 'staff123', name: 'Staff User' },
                waiter: { email: '<EMAIL>', password: 'waiter123', name: 'Waiter User' }
            };

            // Role selection
            roleCards.forEach(card => {
                card.addEventListener('click', () => {
                    // Remove selected class from all cards
                    roleCards.forEach(c => c.classList.remove('selected'));
                    
                    // Add selected class to clicked card
                    card.classList.add('selected');
                    
                    // Set the selected role
                    const role = card.dataset.role;
                    selectedRoleInput.value = role;
                    
                    // Auto-fill demo credentials
                    if (demoAccounts[role]) {
                        document.getElementById('email').value = demoAccounts[role].email;
                        document.getElementById('password').value = demoAccounts[role].password;
                    }
                    
                    console.log('Selected role:', role);
                });
            });

            // Password toggle
            togglePasswordBtn.addEventListener('click', () => {
                const type = passwordField.type === 'password' ? 'text' : 'password';
                passwordField.type = type;
                
                const icon = togglePasswordBtn.querySelector('i');
                icon.setAttribute('data-lucide', type === 'password' ? 'eye' : 'eye-off');
                lucide.createIcons();
            });

            // Form validation
            const validateField = (field) => {
                const fieldGroup = field.closest('.form-group') || field.closest('div');
                const validationMessage = fieldGroup.querySelector('.validation-message');
                
                field.classList.remove('error', 'success');
                if (validationMessage) validationMessage.innerHTML = '';
                
                if (field.name === 'role' && !field.value) {
                    if (validationMessage) {
                        validationMessage.innerHTML = '<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> Please select a role</div>';
                    }
                    lucide.createIcons();
                    return false;
                }
                
                if (field.required && !field.value.trim()) {
                    field.classList.add('error');
                    if (validationMessage) {
                        validationMessage.innerHTML = '<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> This field is required</div>';
                    }
                    lucide.createIcons();
                    return false;
                }
                
                if (field.type === 'email' && field.value) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(field.value)) {
                        field.classList.add('error');
                        if (validationMessage) {
                            validationMessage.innerHTML = '<div class="error-message"><i data-lucide="alert-circle" class="w-3 h-3"></i> Please enter a valid email</div>';
                        }
                        lucide.createIcons();
                        return false;
                    }
                }
                
                field.classList.add('success');
                if (validationMessage) {
                    validationMessage.innerHTML = '<div class="success-message"><i data-lucide="check-circle" class="w-3 h-3"></i> Looks good!</div>';
                }
                lucide.createIcons();
                return true;
            };

            // Real-time validation
            form.addEventListener('input', (e) => {
                if (e.target.matches('input')) {
                    validateField(e.target);
                }
            });

            // Form submission
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                
                const formData = new FormData(form);
                const email = formData.get('email');
                const password = formData.get('password');
                const role = formData.get('role');
                
                // Validate all fields
                const fields = form.querySelectorAll('input[required]');
                let isValid = true;
                
                fields.forEach(field => {
                    if (!validateField(field)) {
                        isValid = false;
                    }
                });
                
                // Validate role selection
                if (!validateField(selectedRoleInput)) {
                    isValid = false;
                }
                
                if (!isValid) {
                    return;
                }
                
                // Show loading state
                const btnText = form.querySelector('.btn-text');
                const btnLoading = form.querySelector('.btn-loading');
                const submitBtn = form.querySelector('button[type="submit"]');
                
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
                submitBtn.disabled = true;
                
                // Simulate login process
                setTimeout(() => {
                    // Check credentials
                    const account = demoAccounts[role];
                    if (account && email === account.email && password === account.password) {
                        // Successful login
                        const userData = {
                            email: email,
                            role: role,
                            name: account.name,
                            loginDate: new Date().toISOString(),
                            isLoggedIn: true
                        };
                        
                        localStorage.setItem('restroManagerUser', JSON.stringify(userData));
                        
                        // Show success and redirect
                        const successModal = document.createElement('div');
                        successModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        successModal.innerHTML = `
                            <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="check" class="w-8 h-8 text-green-600"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-slate-800 mb-2">Welcome Back!</h3>
                                <p class="text-slate-600 mb-6">Successfully logged in as ${role}. Redirecting to dashboard...</p>
                            </div>
                        `;
                        
                        document.body.appendChild(successModal);
                        lucide.createIcons();
                        
                        setTimeout(() => {
                            window.location.href = './dashboard.html';
                        }, 2000);
                        
                    } else {
                        // Failed login
                        const errorModal = document.createElement('div');
                        errorModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                        errorModal.innerHTML = `
                            <div class="bg-white p-8 rounded-2xl shadow-2xl max-w-md mx-4 text-center">
                                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i data-lucide="x" class="w-8 h-8 text-red-600"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-slate-800 mb-2">Login Failed</h3>
                                <p class="text-slate-600 mb-6">Invalid email or password. Please try again.</p>
                                <button onclick="this.parentElement.parentElement.remove()" class="btn-primary px-6 py-3 text-white rounded-xl font-semibold">
                                    Try Again
                                </button>
                            </div>
                        `;
                        
                        document.body.appendChild(errorModal);
                        lucide.createIcons();
                        
                        // Reset button state
                        btnText.classList.remove('hidden');
                        btnLoading.classList.add('hidden');
                        submitBtn.disabled = false;
                    }
                }, 1500);
            });

            // Auto-select admin role by default
            setTimeout(() => {
                roleCards[0].click();
            }, 500);
        });
    </script>
</body>
</html>
