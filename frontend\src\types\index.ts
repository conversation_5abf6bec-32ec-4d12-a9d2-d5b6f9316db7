export interface User {
  email: string;
  role: 'admin' | 'manager' | 'staff' | 'waiter';
  name: string;
  loginDate: string;
  isLoggedIn: boolean;
}

export interface AuthContextType {
  user: User | null;
  login: (userData: User) => void;
  logout: () => void;
  isAuthenticated: boolean;
}

export interface FormValidation {
  isValid: boolean;
  message: string;
}

export interface DemoAccount {
  email: string;
  password: string;
  name: string;
}

export interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
  restaurantName: string;
  phone: string;
  address: string;
  agreeToTerms: boolean;
}

export interface LoginFormData {
  email: string;
  password: string;
  role: string;
  rememberMe: boolean;
}

export interface SignupFormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
  role?: string;
  restaurantName?: string;
  phone?: string;
  address?: string;
  agreeToTerms?: string;
}
