import React from 'react';

export const Test: React.FC = () => {
  // Check if Tailwind classes are being applied
  React.useEffect(() => {
    const testDiv = document.createElement('div');
    testDiv.className = 'bg-blue-500';
    testDiv.style.display = 'none';
    document.body.appendChild(testDiv);

    const computedStyle = window.getComputedStyle(testDiv);
    const bgColor = computedStyle.backgroundColor;

    console.log('🎨 Tailwind CSS Test:');
    console.log('Expected: rgb(59, 130, 246)');
    console.log('Actual:', bgColor);
    console.log('Working:', bgColor === 'rgb(59, 130, 246)');

    document.body.removeChild(testDiv);
  }, []);

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center p-4"
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(to bottom right, #eff6ff, #dbeafe)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1rem'
      }}
    >
      <div
        className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full"
        style={{
          backgroundColor: 'white',
          borderRadius: '1rem',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
          padding: '2rem',
          maxWidth: '28rem',
          width: '100%'
        }}
      >
        <h1 className="text-3xl font-bold text-slate-800 mb-4">Tailwind CSS Test</h1>
        <p className="text-slate-600 mb-6">
          If you can see this styled properly, Tailwind CSS is working correctly.
        </p>
        
        <div className="space-y-4">
          <div className="bg-blue-500 text-white p-4 rounded-lg">
            Blue background with white text
          </div>
          
          <div className="bg-green-500 text-white p-4 rounded-lg">
            Green background with white text
          </div>
          
          <div className="bg-red-500 text-white p-4 rounded-lg">
            Red background with white text
          </div>
          
          <button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transition-all duration-300">
            Gradient Button
          </button>
        </div>
        
        <div className="mt-6 grid grid-cols-2 gap-4">
          <div className="bg-slate-100 p-3 rounded text-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full mx-auto mb-2"></div>
            <span className="text-sm text-slate-600">Circle</span>
          </div>
          
          <div className="bg-slate-100 p-3 rounded text-center">
            <div className="w-8 h-8 bg-green-500 rounded mx-auto mb-2"></div>
            <span className="text-sm text-slate-600">Square</span>
          </div>
        </div>
      </div>
    </div>
  );
};
