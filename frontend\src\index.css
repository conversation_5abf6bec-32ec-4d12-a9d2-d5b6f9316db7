@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --success-500: #10b981;
  --error-500: #ef4444;
  --warning-500: #f59e0b;
}

body {
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  margin: 0;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #f1f5f9;
}
::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Glass morphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Form input styles */
.form-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Button styles */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.5);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

/* Animations */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error and success messages */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: slideDown 0.3s ease-out;
}

.success-message {
  color: #10b981;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: slideDown 0.3s ease-out;
}

/* Additional styles for multi-step signup */
.step-section {
  display: none;
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-section.active {
  display: block;
}

.step-indicator {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-indicator .step-circle {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step-indicator.active .step-circle {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border-color: #2563eb;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
  transform: scale(1.1);
}

.step-indicator.active .step-label {
  color: #1e293b;
  font-weight: 600;
}

.step-indicator.completed .step-circle {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-color: #10b981;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.step-indicator.completed .step-label {
  color: #1e293b;
  font-weight: 600;
}

.step-connector {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #e2e8f0;
}

.step-indicator.completed .step-connector {
  background: linear-gradient(to right, #10b981, #059669);
}

.step-indicator.active .step-connector {
  background: linear-gradient(to right, #10b981, #e2e8f0);
}

.selectable-option {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.selectable-option input:checked + label {
  border-color: #2563eb;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
  color: #1d4ed8;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
  transform: translateY(-2px);
}

.selectable-option label:hover {
  border-color: #93c5fd;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-row {
  animation: slideInLeft 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  transition: all 0.3s ease;
}

.table-row:hover {
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  transform: translateX(4px);
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.cuisine-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.drag-drop-area {
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
}

.drag-drop-area.dragover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

/* Password strength indicators */
.strength-weak { background: #ef4444; }
.strength-fair { background: #f59e0b; }
.strength-good { background: #3b82f6; }
.strength-strong { background: #10b981; }
