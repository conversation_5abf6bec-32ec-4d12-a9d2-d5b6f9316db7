import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Smartphone, WifiOff } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Logo } from '../components/ui/Logo';
import { Button } from '../components/ui/Button';

export const Home: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check user authentication status
    const timer = setTimeout(() => {
      if (isAuthenticated && user) {
        // User is logged in, redirect to dashboard
        navigate('/dashboard');
      } else if (user && !user.isLoggedIn) {
        // User has account but not logged in, redirect to login
        navigate('/signin');
      } else {
        // New user, show manual navigation
        setIsLoading(false);
      }
    }, 1000);

    // Fallback: show manual navigation after 3 seconds
    const fallbackTimer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearTimeout(fallbackTimer);
    };
  }, [isAuthenticated, user, navigate]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-2xl shadow-xl p-8">
            <div className="mb-8">
              <Logo size="lg" />
            </div>
            
            <p className="text-slate-600 mb-8">
              Complete Restaurant Management Solution
            </p>
            
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="loading-spinner w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full"></div>
              <span className="text-slate-600">Loading your experience...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-md mx-auto text-center">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="mb-8">
            <Logo size="lg" />
          </div>
          
          <p className="text-slate-600 mb-8">
            Complete Restaurant Management Solution
          </p>
          
          <div className="space-y-4">
            <p className="text-slate-600 mb-6">Choose your path:</p>

            <div className="space-y-3">
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={() => navigate('/signin')}
              >
                Sign In
              </Button>

              <Button
                variant="secondary"
                size="lg"
                className="w-full"
                onClick={() => navigate('/signup')}
              >
                Create Account
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="w-full"
                onClick={() => navigate('/dashboard')}
              >
                Go to Dashboard
              </Button>
            </div>
          </div>
        </div>
        
        {/* Features */}
        <div className="mt-8 grid grid-cols-2 gap-4 text-center">
          <div className="bg-white/50 backdrop-blur-sm rounded-xl p-4">
            <Smartphone className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-slate-700">Mobile Ready</p>
          </div>
          <div className="bg-white/50 backdrop-blur-sm rounded-xl p-4">
            <WifiOff className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-slate-700">Works Offline</p>
          </div>
        </div>
      </div>
    </div>
  );
};
