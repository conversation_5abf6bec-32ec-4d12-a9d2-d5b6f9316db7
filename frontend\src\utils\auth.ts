import { type User, type DemoAccount } from '../types';

const STORAGE_KEY = 'restroManagerUser';

// Demo user accounts for different roles
export const demoAccounts: Record<string, DemoAccount> = {
  admin: { email: '<EMAIL>', password: 'admin123', name: 'Admin User' },
  manager: { email: '<EMAIL>', password: 'manager123', name: 'Manager User' },
  staff: { email: '<EMAIL>', password: 'staff123', name: 'Staff User' },
  waiter: { email: '<EMAIL>', password: 'waiter123', name: 'Waiter User' }
};

export const saveUserToStorage = (user: User): void => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(user));
};

export const getUserFromStorage = (): User | null => {
  try {
    const userData = localStorage.getItem(STORAGE_KEY);
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error('Error parsing user data from storage:', error);
    return null;
  }
};

export const removeUserFromStorage = (): void => {
  localStorage.removeItem(STORAGE_KEY);
};

export const validateCredentials = (email: string, password: string, role: string): boolean => {
  const account = demoAccounts[role];
  return account && email === account.email && password === account.password;
};

export const createUserFromCredentials = (email: string, role: string): User => {
  const account = demoAccounts[role];
  return {
    email,
    role: role as User['role'],
    name: account.name,
    loginDate: new Date().toISOString(),
    isLoggedIn: true
  };
};
