import { type FormValidation } from '../types';

export const validateEmail = (email: string): FormValidation => {
  if (!email.trim()) {
    return { isValid: false, message: 'Email is required' };
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }
  
  return { isValid: true, message: 'Looks good!' };
};

export const validatePassword = (password: string): FormValidation => {
  if (!password.trim()) {
    return { isValid: false, message: 'Password is required' };
  }
  
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters' };
  }
  
  return { isValid: true, message: 'Looks good!' };
};

export const validateConfirmPassword = (password: string, confirmPassword: string): FormValidation => {
  if (!confirmPassword.trim()) {
    return { isValid: false, message: 'Please confirm your password' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, message: 'Passwords do not match' };
  }
  
  return { isValid: true, message: 'Passwords match!' };
};

export const validateRequired = (value: string, fieldName: string): FormValidation => {
  if (!value.trim()) {
    return { isValid: false, message: `${fieldName} is required` };
  }
  
  return { isValid: true, message: 'Looks good!' };
};

export const validatePhone = (phone: string): FormValidation => {
  if (!phone.trim()) {
    return { isValid: false, message: 'Phone number is required' };
  }
  
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
    return { isValid: false, message: 'Please enter a valid phone number' };
  }
  
  return { isValid: true, message: 'Looks good!' };
};
